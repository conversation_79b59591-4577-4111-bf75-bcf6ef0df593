//
//  User.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation

struct User: Codable, Identifiable {
    let id: UUID
    var email: String
    var name: String
    var profileImageURL: String?
    var subscriptionType: SubscriptionType
    var createdAt: Date
    var lastLoginAt: Date
    var preferences: UserPreferences
    var stats: UserStats
    
    init(email: String, name: String) {
        self.id = UUID()
        self.email = email
        self.name = name
        self.subscriptionType = .free
        self.createdAt = Date()
        self.lastLoginAt = Date()
        self.preferences = UserPreferences()
        self.stats = UserStats()
    }
}

struct UserPreferences: Codable {
    var language: String = "en"
    var notificationsEnabled: Bool = true
    var reminderTime: Date?
    var theme: AppTheme = .system
    var soundEnabled: Bool = true
    var vibrationEnabled: Bool = true
}

struct UserStats: Codable {
    var totalSessions: Int = 0
    var totalMinutes: Int = 0
    var currentStreak: Int = 0
    var longestStreak: Int = 0
    var favoriteBreathingTechnique: String?
    var lastSessionDate: Date?
}

enum SubscriptionType: String, Codable, CaseIterable {
    case free = "free"
    case premium = "premium"
    case lifetime = "lifetime"
    
    var displayName: String {
        switch self {
        case .free:
            return "Free"
        case .premium:
            return "Premium"
        case .lifetime:
            return "Lifetime"
        }
    }
}

enum AppTheme: String, Codable, CaseIterable {
    case light = "light"
    case dark = "dark"
    case system = "system"
    
    var displayName: String {
        switch self {
        case .light:
            return "Light"
        case .dark:
            return "Dark"
        case .system:
            return "System"
        }
    }
}
