//
//  AuthenticationView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct AuthenticationView: View {
    @StateObject private var authService = AuthenticationService.shared
    @State private var isSignUp = false
    @State private var email = ""
    @State private var password = ""
    @State private var name = ""
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                    VStack(spacing: SerenityTheme.Spacing.xl) {
                        // Header
                        VStack(spacing: SerenityTheme.Spacing.md) {
                            Text("Serenity")
                                .font(SerenityTheme.Typography.largeTitle)
                                .foregroundColor(SerenityTheme.Colors.textPrimary)
                            
                            Text(isSignUp ? "Create your account" : "Log into your account")
                                .font(SerenityTheme.Typography.body)
                                .foregroundColor(SerenityTheme.Colors.textSecondary)
                        }
                        .padding(.top, SerenityTheme.Spacing.xl)
                        
                        // Email/Password Form
                        VStack(spacing: SerenityTheme.Spacing.lg) {
                            if isSignUp {
                                TextField("Name", text: $name)
                                    .serenityTextFieldStyle()
                            }
                            
                            TextField("Email", text: $email)
                                .keyboardType(.emailAddress)
                                .autocapitalization(.none)
                                .serenityTextFieldStyle()
                            
                            SecureField("Password", text: $password)
                                .serenityTextFieldStyle()
                            
                            Button(action: {
                                if isSignUp {
                                    authService.signUpWithEmail(email: email, password: password, name: name)
                                } else {
                                    authService.signInWithEmail(email: email, password: password)
                                }
                            }) {
                                if authService.isLoading {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                } else {
                                    Text(isSignUp ? "Continue with Email" : "Continue with Email")
                                }
                            }
                            .serenityButtonStyle()
                            .disabled(authService.isLoading)
                        }
                        .padding(.horizontal, SerenityTheme.Spacing.xl)
                        
                        // Or continue with
                        VStack(spacing: SerenityTheme.Spacing.md) {
                            Text("Or continue with")
                                .font(SerenityTheme.Typography.footnote)
                                .foregroundColor(SerenityTheme.Colors.textTertiary)
                            
                            // Social Login Buttons
                            VStack(spacing: SerenityTheme.Spacing.md) {
                                SocialLoginButton(
                                    title: "Continue with Apple ID",
                                    iconName: "applelogo",
                                    action: { authService.signInWithApple() }
                                )
                                
                                SocialLoginButton(
                                    title: "Continue with Google",
                                    iconName: "globe",
                                    action: { authService.signInWithGoogle() }
                                )
                                
                                SocialLoginButton(
                                    title: "Continue with TikTok",
                                    iconName: "music.note",
                                    action: { authService.signInWithTikTok() }
                                )
                                
                                SocialLoginButton(
                                    title: "Continue with Facebook",
                                    iconName: "person.2",
                                    action: { authService.signInWithFacebook() }
                                )
                            }
                        }
                        .padding(.horizontal, SerenityTheme.Spacing.xl)
                        
                        // Toggle Sign Up/Sign In
                        Button(action: {
                            isSignUp.toggle()
                            name = ""
                            email = ""
                            password = ""
                        }) {
                            HStack {
                                Text(isSignUp ? "Already have an account?" : "Don't have an account?")
                                    .foregroundColor(SerenityTheme.Colors.textSecondary)
                                Text(isSignUp ? "Sign In" : "Sign Up")
                                    .foregroundColor(SerenityTheme.Colors.primary)
                                    .fontWeight(.semibold)
                            }
                            .font(SerenityTheme.Typography.callout)
                        }
                        
                        // Terms and Privacy
                        Text("By continuing, you agree to our Terms of Service and Privacy Policy")
                            .font(SerenityTheme.Typography.caption2)
                            .foregroundColor(SerenityTheme.Colors.textTertiary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, SerenityTheme.Spacing.xl)
                        
                        Spacer()
                    }
            }
        .serenityOceanBackground()
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Cancel") {
                    dismiss()
                }
                .foregroundColor(SerenityTheme.Colors.textSecondary)
            }
        }
        .alert("Error", isPresented: .constant(authService.errorMessage != nil)) {
            Button("OK") {
                authService.errorMessage = nil
            }
        } message: {
            Text(authService.errorMessage ?? "")
        }
        .onChange(of: authService.isAuthenticated) { _, isAuthenticated in
            if isAuthenticated {
                dismiss()
            }
        }
        }
    }
}

struct SocialLoginButton: View {
    let title: String
    let iconName: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: iconName)
                    .font(.system(size: 16, weight: .medium))
                
                Text(title)
                    .font(SerenityTheme.Typography.callout)
                    .fontWeight(.medium)
                
                Spacer()
            }
            .foregroundColor(SerenityTheme.Colors.textPrimary)
            .padding(.horizontal, SerenityTheme.Spacing.lg)
            .padding(.vertical, SerenityTheme.Spacing.md)
            .background(SerenityTheme.Colors.cardBackground)
            .cornerRadius(SerenityTheme.CornerRadius.small)
            .overlay(
                RoundedRectangle(cornerRadius: SerenityTheme.CornerRadius.small)
                    .stroke(SerenityTheme.Colors.textTertiary.opacity(0.2), lineWidth: 1)
            )
        }
    }
}

#Preview {
    AuthenticationView()
}
