//
//  BreathingView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct BreathingView: View {
    @State private var selectedCategory: BreathingCategory? = nil
    @State private var selectedTechnique: BreathingTechnique?
    
    private var filteredTechniques: [BreathingTechnique] {
        let techniques = BreathingTechnique.allTechniques

        if let category = selectedCategory {
            return techniques.filter { $0.category == category }
        }

        return techniques
    }
    
    private var groupedTechniques: [BreathingCategory: [BreathingTechnique]] {
        Dictionary(grouping: filteredTechniques) { $0.category }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                    // Header
                    headerSection
                    
                    // Categories Filter
                    categoriesSection
                    
                    // Techniques List
                    techniquesSection
            }
            .serenityOceanBackground()
            .navigationBarHidden(true)
        }
        .sheet(item: $selectedTechnique) { technique in
            BreathingExerciseView(technique: technique)
        }
    }
    
    private var headerSection: some View {
        HStack {
            Text("Breathe")
                .font(SerenityTheme.Typography.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            Spacer()

            Button(action: {}) {
                Image(systemName: "questionmark.circle")
                    .font(.system(size: 20))
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
            }
        }
        .padding(.horizontal, SerenityTheme.Spacing.lg)
        .padding(.top, SerenityTheme.Spacing.lg)
    }
    
    private var categoriesSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: SerenityTheme.Spacing.sm) {
                // All Categories Button
                CategoryButton(
                    title: "All",
                    isSelected: selectedCategory == nil,
                    action: { selectedCategory = nil }
                )
                
                ForEach(BreathingCategory.allCases, id: \.self) { category in
                    CategoryButton(
                        title: category.displayName,
                        isSelected: selectedCategory == category,
                        action: { 
                            selectedCategory = selectedCategory == category ? nil : category
                        }
                    )
                }
            }
            .padding(.horizontal, SerenityTheme.Spacing.lg)
        }
        .padding(.vertical, SerenityTheme.Spacing.md)
    }
    
    private var techniquesSection: some View {
        ScrollView {
            LazyVStack(spacing: SerenityTheme.Spacing.lg) {
                if selectedCategory != nil {
                    // Show techniques for selected category
                    if let techniques = groupedTechniques[selectedCategory!] {
                        ForEach(techniques) { technique in
                            BreathingTechniqueCard(technique: technique) {
                                selectedTechnique = technique
                            }
                        }
                    }
                } else {
                    // Show all categories with their techniques
                    ForEach(BreathingCategory.allCases, id: \.self) { category in
                        if let techniques = groupedTechniques[category], !techniques.isEmpty {
                            CategorySection(category: category, techniques: techniques) { technique in
                                selectedTechnique = technique
                            }
                        }
                    }
                }
            }
            .padding(.horizontal, SerenityTheme.Spacing.lg)
            .padding(.bottom, SerenityTheme.Spacing.xl)
        }
    }
}

struct CategoryButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(SerenityTheme.Typography.callout)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : SerenityTheme.Colors.textSecondary)
                .padding(.horizontal, SerenityTheme.Spacing.md)
                .padding(.vertical, SerenityTheme.Spacing.sm)
                .background(
                    isSelected ? SerenityTheme.Colors.primary : SerenityTheme.Colors.cardBackground
                )
                .cornerRadius(SerenityTheme.CornerRadius.small)
        }
    }
}

struct CategorySection: View {
    let category: BreathingCategory
    let techniques: [BreathingTechnique]
    let onTechniqueSelected: (BreathingTechnique) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text(category.displayName)
                .font(SerenityTheme.Typography.title3)
                .fontWeight(.semibold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            VStack(spacing: SerenityTheme.Spacing.sm) {
                ForEach(techniques) { technique in
                    BreathingTechniqueCard(technique: technique) {
                        onTechniqueSelected(technique)
                    }
                }
            }
        }
    }
}

struct BreathingTechniqueCard: View {
    let technique: BreathingTechnique
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: SerenityTheme.Spacing.md) {
                // Technique Image
                RoundedRectangle(cornerRadius: SerenityTheme.CornerRadius.small)
                    .fill(technique.category.color.opacity(0.3))
                    .frame(width: 60, height: 60)
                    .overlay(
                        Image(systemName: iconForTechnique(technique))
                            .font(.system(size: 20))
                            .foregroundColor(technique.category.color)
                    )

                VStack(alignment: .leading, spacing: 4) {
                    HStack(alignment: .top) {
                        Text(technique.name)
                            .font(SerenityTheme.Typography.headline)
                            .foregroundColor(SerenityTheme.Colors.textPrimary)
                            .multilineTextAlignment(.leading)
                            .frame(maxWidth: .infinity, alignment: .leading)

                        if technique.isPremium {
                            Image(systemName: "crown.fill")
                                .font(.system(size: 12))
                                .foregroundColor(SerenityTheme.Colors.warning)
                        }
                    }

                    Text(technique.description)
                        .font(SerenityTheme.Typography.callout)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    HStack {
                        Text("\(Int(technique.duration / 60)) min")
                            .font(SerenityTheme.Typography.caption1)
                            .foregroundColor(SerenityTheme.Colors.primary)

                        Text("•")
                            .foregroundColor(SerenityTheme.Colors.textTertiary)

                        Text(technique.difficulty.displayName)
                            .font(SerenityTheme.Typography.caption1)
                            .foregroundColor(SerenityTheme.Colors.textTertiary)
                    }
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(SerenityTheme.Colors.textTertiary)
            }
            .padding(SerenityTheme.Spacing.md)
            .serenityCardStyle()
        }
    }
    
    private func iconForTechnique(_ technique: BreathingTechnique) -> String {
        switch technique.name {
        case "Diaphragmatic Breathing":
            return "lungs"
        case "Equal Breathing":
            return "equal.circle"
        case "Alternate Nostril Breathing":
            return "arrow.left.arrow.right"
        case "Complete Breathing":
            return "circle.grid.3x3"
        case "Box 4-4-4 Breathing":
            return "square"
        case "4-7-8 Breathing":
            return "moon.stars"
        case "Bellows Breath":
            return "flame"
        default:
            return "wind"
        }
    }
}

#Preview {
    BreathingView()
}
