//
//  MainTabView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct MainTabView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            HomeView()
                .tabItem {
                    Image(systemName: selectedTab == 0 ? "house.fill" : "house")
                    Text("Home")
                }
                .tag(0)
            
            BreathingView()
                .tabItem {
                    Image(systemName: selectedTab == 1 ? "wind.circle.fill" : "wind.circle")
                    Text("Breathe")
                }
                .tag(1)
            
            SleepView()
                .tabItem {
                    Image(systemName: selectedTab == 2 ? "moon.fill" : "moon")
                    Text("Sleep")
                }
                .tag(2)
            
            MeditateView()
                .tabItem {
                    Image(systemName: selectedTab == 3 ? "brain.head.profile.fill" : "brain.head.profile")
                    Text("Meditate")
                }
                .tag(3)
            
            MoreView()
                .tabItem {
                    Image(systemName: selectedTab == 4 ? "person.circle.fill" : "person.circle")
                    Text("Profile")
                }
                .tag(4)
        }
        .accentColor(SerenityTheme.Colors.primary)
        .preferredColorScheme(.dark)
    }
}

#Preview {
    MainTabView()
}
