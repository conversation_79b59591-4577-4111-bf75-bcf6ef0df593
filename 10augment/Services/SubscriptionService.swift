//
//  SubscriptionService.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import StoreKit
import Combine

class SubscriptionService: NSObject, ObservableObject {
    @Published var subscriptionType: SubscriptionType = .free
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var availableProducts: [SKProduct] = []
    
    private var cancellables = Set<AnyCancellable>()
    
    // Product identifiers for in-app purchases
    private let productIdentifiers: Set<String> = [
        "com.serenity.premium.monthly",
        "com.serenity.premium.yearly",
        "com.serenity.lifetime"
    ]
    
    override init() {
        super.init()
        loadSubscriptionStatus()
        requestProducts()
        SKPaymentQueue.default().add(self)
    }

    deinit {
        SKPaymentQueue.default().remove(self)
    }
    
    // MARK: - Public Methods
    
    func loadSubscriptionStatus() {
        // Load subscription status from UserDefaults or server
        if let subscriptionString = UserDefaults.standard.string(forKey: "subscriptionType"),
           let subscription = SubscriptionType(rawValue: subscriptionString) {
            self.subscriptionType = subscription
        }
    }
    
    func purchaseProduct(_ product: SKProduct) {
        guard SKPaymentQueue.canMakePayments() else {
            errorMessage = "In-app purchases are not available on this device"
            return
        }
        
        isLoading = true
        let payment = SKPayment(product: product)
        SKPaymentQueue.default().add(payment)
    }
    
    func restorePurchases() {
        isLoading = true
        SKPaymentQueue.default().restoreCompletedTransactions()
    }
    
    func hasAccess(to technique: BreathingTechnique) -> Bool {
        if !technique.isPremium {
            return true
        }
        
        return subscriptionType == .premium || subscriptionType == .lifetime
    }
    
    // MARK: - Private Methods
    
    private func requestProducts() {
        let request = SKProductsRequest(productIdentifiers: productIdentifiers)
        request.delegate = self
        request.start()
    }
    
    private func updateSubscriptionType(_ type: SubscriptionType) {
        subscriptionType = type
        UserDefaults.standard.set(type.rawValue, forKey: "subscriptionType")
    }
}

// MARK: - SKProductsRequestDelegate
extension SubscriptionService: SKProductsRequestDelegate {
    func productsRequest(_ request: SKProductsRequest, didReceive response: SKProductsResponse) {
        DispatchQueue.main.async {
            self.availableProducts = response.products
            self.isLoading = false
        }
    }
    
    func request(_ request: SKRequest, didFailWithError error: Error) {
        DispatchQueue.main.async {
            self.errorMessage = "Failed to load products: \(error.localizedDescription)"
            self.isLoading = false
        }
    }
}

// MARK: - SKPaymentTransactionObserver
extension SubscriptionService: SKPaymentTransactionObserver {
    func paymentQueue(_ queue: SKPaymentQueue, updatedTransactions transactions: [SKPaymentTransaction]) {
        for transaction in transactions {
            switch transaction.transactionState {
            case .purchased:
                handlePurchase(transaction)
            case .restored:
                handleRestore(transaction)
            case .failed:
                handleFailure(transaction)
            case .deferred, .purchasing:
                break
            @unknown default:
                break
            }
        }
    }
    
    private func handlePurchase(_ transaction: SKPaymentTransaction) {
        DispatchQueue.main.async {
            self.isLoading = false
            
            switch transaction.payment.productIdentifier {
            case "com.serenity.premium.monthly", "com.serenity.premium.yearly":
                self.updateSubscriptionType(.premium)
            case "com.serenity.lifetime":
                self.updateSubscriptionType(.lifetime)
            default:
                break
            }
        }
        
        SKPaymentQueue.default().finishTransaction(transaction)
    }
    
    private func handleRestore(_ transaction: SKPaymentTransaction) {
        handlePurchase(transaction)
    }
    
    private func handleFailure(_ transaction: SKPaymentTransaction) {
        DispatchQueue.main.async {
            self.isLoading = false
            if let error = transaction.error as? SKError {
                if error.code != .paymentCancelled {
                    self.errorMessage = "Purchase failed: \(error.localizedDescription)"
                }
            }
        }
        
        SKPaymentQueue.default().finishTransaction(transaction)
    }
}

// MARK: - Subscription Plans
struct SubscriptionPlan {
    let id: String
    let name: String
    let description: String
    let price: String
    let duration: String
    let features: [String]
    
    static let monthly = SubscriptionPlan(
        id: "com.serenity.premium.monthly",
        name: "Premium Monthly",
        description: "Full access to all breathing techniques",
        price: "$4.99",
        duration: "per month",
        features: [
            "Access to all breathing techniques",
            "Advanced breathing patterns",
            "Personalized recommendations",
            "Progress tracking",
            "No ads"
        ]
    )
    
    static let yearly = SubscriptionPlan(
        id: "com.serenity.premium.yearly",
        name: "Premium Yearly",
        description: "Full access with 50% savings",
        price: "$29.99",
        duration: "per year",
        features: [
            "Access to all breathing techniques",
            "Advanced breathing patterns",
            "Personalized recommendations",
            "Progress tracking",
            "No ads",
            "50% savings vs monthly"
        ]
    )
    
    static let lifetime = SubscriptionPlan(
        id: "com.serenity.lifetime",
        name: "Lifetime Access",
        description: "One-time purchase, lifetime access",
        price: "$99.99",
        duration: "one-time",
        features: [
            "Access to all breathing techniques",
            "Advanced breathing patterns",
            "Personalized recommendations",
            "Progress tracking",
            "No ads",
            "Lifetime updates",
            "Best value"
        ]
    )
    
    static let allPlans = [monthly, yearly, lifetime]
}
