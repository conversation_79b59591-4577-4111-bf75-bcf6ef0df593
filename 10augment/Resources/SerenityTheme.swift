//
//  SerenityTheme.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct SerenityTheme {
    // MARK: - Colors
    struct Colors {
        // Primary Colors
        static let primary = Color(red: 0.2, green: 0.6, blue: 0.9) // Blue from prototype
        static let primaryDark = Color(red: 0.1, green: 0.4, blue: 0.7)
        static let secondary = Color(red: 0.9, green: 0.9, blue: 0.95)
        
        // Background Colors - Ocean Gradient
        static let backgroundGradient = LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 0.1, green: 0.3, blue: 0.6),
                Color(red: 0.2, green: 0.5, blue: 0.8),
                Color(red: 0.3, green: 0.7, blue: 0.9)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        static let background = Color(red: 0.1, green: 0.3, blue: 0.6) // Primary gradient color
        static let cardBackground = Color(red: 0.1, green: 0.2, blue: 0.4).opacity(0.6)
        static let surfaceBackground = Color(red: 0.15, green: 0.25, blue: 0.5).opacity(0.4)
        
        // Text Colors
        static let textPrimary = Color.white
        static let textSecondary = Color(red: 0.7, green: 0.8, blue: 0.9)
        static let textTertiary = Color(red: 0.5, green: 0.6, blue: 0.7)
        
        // Accent Colors
        static let accent = Color(red: 0.3, green: 0.8, blue: 0.6) // Green accent
        static let warning = Color(red: 1.0, green: 0.6, blue: 0.2)
        static let error = Color(red: 0.9, green: 0.3, blue: 0.3)
        static let success = Color(red: 0.3, green: 0.8, blue: 0.4)
        
        // Breathing Exercise Colors
        static let breathingInhale = Color(red: 0.4, green: 0.8, blue: 1.0)
        static let breathingExhale = Color(red: 0.8, green: 0.6, blue: 1.0)
        static let breathingHold = Color(red: 1.0, green: 0.8, blue: 0.4)
        
        // Category Colors
        static let quickExercises = Color(red: 0.3, green: 0.7, blue: 0.9)
        static let meditate = Color(red: 0.8, green: 0.4, blue: 0.9)
        static let insomnia = Color(red: 0.4, green: 0.3, blue: 0.8)
        static let stressReduction = Color(red: 0.9, green: 0.6, blue: 0.3)
        static let sports = Color(red: 0.9, green: 0.3, blue: 0.4)
    }
    
    // MARK: - Typography
    struct Typography {
        static let largeTitle = Font.system(size: 34, weight: .bold, design: .rounded)
        static let title1 = Font.system(size: 28, weight: .bold, design: .rounded)
        static let title2 = Font.system(size: 22, weight: .semibold, design: .rounded)
        static let title3 = Font.system(size: 20, weight: .semibold, design: .rounded)
        static let headline = Font.system(size: 17, weight: .semibold, design: .rounded)
        static let body = Font.system(size: 17, weight: .regular, design: .rounded)
        static let callout = Font.system(size: 16, weight: .regular, design: .rounded)
        static let subheadline = Font.system(size: 15, weight: .regular, design: .rounded)
        static let footnote = Font.system(size: 13, weight: .regular, design: .rounded)
        static let caption1 = Font.system(size: 12, weight: .regular, design: .rounded)
        static let caption2 = Font.system(size: 11, weight: .regular, design: .rounded)
    }
    
    // MARK: - Spacing
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 16
        static let lg: CGFloat = 24
        static let xl: CGFloat = 32
        static let xxl: CGFloat = 48
    }
    
    // MARK: - Corner Radius
    struct CornerRadius {
        static let small: CGFloat = 8
        static let medium: CGFloat = 12
        static let large: CGFloat = 16
        static let extraLarge: CGFloat = 24
    }
    
    // MARK: - Shadows
    struct Shadows {
        static let small = Shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        static let medium = Shadow(color: Color.black.opacity(0.15), radius: 4, x: 0, y: 2)
        static let large = Shadow(color: Color.black.opacity(0.2), radius: 8, x: 0, y: 4)
    }
}

struct Shadow {
    let color: Color
    let radius: CGFloat
    let x: CGFloat
    let y: CGFloat
}

// MARK: - View Extensions
extension View {
    func serenityOceanBackground() -> some View {
        self
            .background(
                ZStack {
                    SerenityTheme.Colors.backgroundGradient
                        .ignoresSafeArea()

                    // Dark overlay for better text readability
                    Color.black.opacity(0.2)
                        .ignoresSafeArea()
                }
            )
    }

    func serenityCardStyle() -> some View {
        self
            .background(SerenityTheme.Colors.cardBackground)
            .cornerRadius(SerenityTheme.CornerRadius.medium)
            .shadow(color: Color.black.opacity(0.2), radius: 4, x: 0, y: 2)
    }
    
    func serenityButtonStyle() -> some View {
        self
            .font(SerenityTheme.Typography.headline)
            .foregroundColor(SerenityTheme.Colors.textPrimary)
            .padding(.horizontal, SerenityTheme.Spacing.lg)
            .padding(.vertical, SerenityTheme.Spacing.md)
            .background(SerenityTheme.Colors.primary)
            .cornerRadius(SerenityTheme.CornerRadius.medium)
    }
    
    func serenitySecondaryButtonStyle() -> some View {
        self
            .font(SerenityTheme.Typography.headline)
            .foregroundColor(SerenityTheme.Colors.primary)
            .padding(.horizontal, SerenityTheme.Spacing.lg)
            .padding(.vertical, SerenityTheme.Spacing.md)
            .background(SerenityTheme.Colors.cardBackground)
            .overlay(
                RoundedRectangle(cornerRadius: SerenityTheme.CornerRadius.medium)
                    .stroke(SerenityTheme.Colors.primary, lineWidth: 1)
            )
    }
    
    func serenityTextFieldStyle() -> some View {
        self
            .font(SerenityTheme.Typography.body)
            .foregroundColor(SerenityTheme.Colors.textPrimary)
            .padding(SerenityTheme.Spacing.md)
            .background(SerenityTheme.Colors.cardBackground)
            .cornerRadius(SerenityTheme.CornerRadius.small)
            .overlay(
                RoundedRectangle(cornerRadius: SerenityTheme.CornerRadius.small)
                    .stroke(SerenityTheme.Colors.textTertiary.opacity(0.3), lineWidth: 1)
            )
    }
}

// MARK: - Category Color Helper
extension BreathingCategory {
    var color: Color {
        switch self {
        case .quickExercises:
            return SerenityTheme.Colors.quickExercises
        case .meditate: // Meditate
            return SerenityTheme.Colors.meditate
        case .insomnia:
            return SerenityTheme.Colors.insomnia
        case .stressReduction: // Stress Reduction
            return SerenityTheme.Colors.stressReduction
        case .sports:
            return SerenityTheme.Colors.sports
        }
    }
}
