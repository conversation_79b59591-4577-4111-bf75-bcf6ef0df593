//
//  TermsOfServiceView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct TermsOfServiceView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: SerenityTheme.Spacing.lg) {
                    // Header
                    headerSection
                    
                    // Terms Content
                    termsContentSection
                }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
                .padding(.vertical, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationTitle("Terms of Service")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(SerenityTheme.Colors.primary)
                }
            }
        }
    }
    
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Terms of Service")
                .font(SerenityTheme.Typography.title1)
                .fontWeight(.bold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            Text("Last updated: July 18, 2025")
                .font(SerenityTheme.Typography.caption1)
                .foregroundColor(SerenityTheme.Colors.textSecondary)
            
            Text("Please read these Terms of Service carefully before using the Serenity app.")
                .font(SerenityTheme.Typography.body)
                .foregroundColor(SerenityTheme.Colors.textSecondary)
        }
    }
    
    private var termsContentSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.xl) {
            TermsSection(
                title: "1. Acceptance of Terms",
                content: "By downloading, installing, or using the Serenity app, you agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use our service."
            )
            
            TermsSection(
                title: "2. Description of Service",
                content: "Serenity is a mobile application that provides guided breathing exercises and meditation techniques. Our service includes both free and premium features designed to help users manage stress, improve sleep, and enhance overall well-being."
            )
            
            TermsSection(
                title: "3. User Accounts",
                content: "You may be required to create an account to access certain features. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account."
            )
            
            TermsSection(
                title: "4. Premium Subscription",
                content: "Premium features require a paid subscription. Subscriptions automatically renew unless cancelled at least 24 hours before the end of the current period. You can manage your subscription through your device's app store settings."
            )
            
            TermsSection(
                title: "5. Health Disclaimer",
                content: "Serenity is designed for wellness and relaxation purposes. It is not intended to diagnose, treat, cure, or prevent any medical condition. Please consult with a healthcare professional before using our app if you have any medical concerns."
            )
            
            TermsSection(
                title: "6. Intellectual Property",
                content: "All content, features, and functionality of the Serenity app are owned by us and are protected by copyright, trademark, and other intellectual property laws."
            )
            
            TermsSection(
                title: "7. Privacy",
                content: "Your privacy is important to us. Please review our Privacy Policy to understand how we collect, use, and protect your information."
            )
            
            TermsSection(
                title: "8. Prohibited Uses",
                content: "You may not use our service for any unlawful purpose or in any way that could damage, disable, or impair our service. You agree not to attempt to gain unauthorized access to our systems."
            )
            
            TermsSection(
                title: "9. Limitation of Liability",
                content: "To the maximum extent permitted by law, we shall not be liable for any indirect, incidental, special, or consequential damages arising from your use of the app."
            )
            
            TermsSection(
                title: "10. Changes to Terms",
                content: "We reserve the right to modify these terms at any time. We will notify users of any material changes through the app or via email. Continued use of the service constitutes acceptance of the modified terms."
            )
            
            TermsSection(
                title: "11. Contact Information",
                content: "If you have any questions about these Terms of Service, please contact <NAME_EMAIL> or through the Contact Us section in the app."
            )
        }
    }
}

struct TermsSection: View {
    let title: String
    let content: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.sm) {
            Text(title)
                .font(SerenityTheme.Typography.headline)
                .fontWeight(.semibold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            Text(content)
                .font(SerenityTheme.Typography.body)
                .foregroundColor(SerenityTheme.Colors.textSecondary)
                .lineSpacing(4)
        }
        .padding(SerenityTheme.Spacing.lg)
        .serenityCardStyle()
    }
}

#Preview {
    TermsOfServiceView()
}
