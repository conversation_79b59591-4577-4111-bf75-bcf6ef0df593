//
//  ProfileView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct ProfileView: View {
    @StateObject private var authService = AuthenticationService.shared
    @Environment(\.dismiss) private var dismiss
    @State private var showingImagePicker = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                    VStack(spacing: SerenityTheme.Spacing.xl) {
                        // Profile Header
                        profileHeaderSection
                    }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
                .padding(.vertical, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationTitle("profile".localized())
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("done".localized()) {
                        dismiss()
                    }
                    .foregroundColor(SerenityTheme.Colors.primary)
                }
            }
        }
    }
    
    private var profileHeaderSection: some View {
        VStack(spacing: SerenityTheme.Spacing.lg) {
            // Profile Image
            Button(action: { showingImagePicker = true }) {
                AsyncImage(url: URL(string: authService.currentUser?.profileImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Circle()
                        .fill(SerenityTheme.Colors.primary)
                        .overlay(
                            Text(authService.currentUser?.name.prefix(1).uppercased() ?? "U")
                                .font(SerenityTheme.Typography.title1)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        )
                }
                .frame(width: 100, height: 100)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(SerenityTheme.Colors.primary, lineWidth: 3)
                )
            }
            
            VStack(spacing: 4) {
                Text(authService.currentUser?.name ?? "user".localized())
                    .font(SerenityTheme.Typography.title2)
                    .fontWeight(.bold)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)
                
                Text(authService.currentUser?.email ?? "<EMAIL>")
                    .font(SerenityTheme.Typography.callout)
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
                
                HStack {
                    Image(systemName: "crown.fill")
                        .font(.system(size: 12))
                        .foregroundColor(SerenityTheme.Colors.warning)
                    
                    Text(authService.currentUser?.subscriptionType.displayName ?? "free".localized())
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.textTertiary)
                }
            }
        }
    }
}

struct UsageStreaksView: View {
    @StateObject private var usageManager = UsageHistoryManager()
    @State private var currentMonth = Date()

    private let calendar = Calendar.current
    private let dateFormatter = DateFormatter.monthYear

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header with stats
                    statsHeader

                    // Calendar view
                    calendarView

                    // Share button
                    shareButton
                }
                .padding()
            }
            .navigationTitle("My Streaks")
            .navigationBarTitleDisplayMode(.large)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.blue.opacity(0.8),
                        Color.purple.opacity(0.8)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
            )
        }
    }

    private var statsHeader: some View {
        HStack(spacing: 40) {
            StatCard(
                title: "Total",
                value: "\(usageManager.history.totalDays) days",
                icon: "eyeglasses",
                color: .white
            )

            StatCard(
                title: "Longest",
                value: "\(usageManager.history.longestStreak) days",
                icon: "arrow.right",
                color: .white
            )

            StatCard(
                title: "Current",
                value: "\(usageManager.history.currentStreak) days",
                icon: "clock",
                color: .white
            )
        }
        .foregroundColor(.white)
    }

    private var calendarView: some View {
        VStack(spacing: 16) {
            // Month navigation
            HStack {
                Button(action: previousMonth) {
                    Image(systemName: "chevron.left")
                        .foregroundColor(.white)
                        .font(.title2)
                }

                Spacer()

                Text(dateFormatter.string(from: currentMonth))
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)

                Spacer()

                Button(action: nextMonth) {
                    Image(systemName: "chevron.right")
                        .foregroundColor(.white)
                        .font(.title2)
                }
            }
            .padding(.horizontal)

            // Calendar grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 8) {
                // Weekday headers
                ForEach(["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"], id: \.self) { day in
                    Text(day)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.white.opacity(0.7))
                        .frame(height: 30)
                }

                // Calendar days
                ForEach(calendarDays, id: \.self) { date in
                    CalendarDayView(
                        date: date,
                        hasUsage: usageManager.history.hasUsage(for: date),
                        isCurrentMonth: calendar.isDate(date, equalTo: currentMonth, toGranularity: .month)
                    )
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
        )
    }

    private var shareButton: some View {
        Button(action: shareStreaks) {
            HStack {
                Image(systemName: "square.and.arrow.up")
                Text("Share My Streaks")
                    .fontWeight(.semibold)
            }
            .foregroundColor(.blue)
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.white)
            .cornerRadius(25)
        }
    }

    private var calendarDays: [Date] {
        guard let monthInterval = calendar.dateInterval(of: .month, for: currentMonth) else {
            return []
        }

        let firstOfMonth = monthInterval.start
        let firstWeekday = calendar.component(.weekday, from: firstOfMonth)

        // Adjust for Monday as first day of week
        let daysFromMonday = (firstWeekday + 5) % 7

        guard let startDate = calendar.date(byAdding: .day, value: -daysFromMonday, to: firstOfMonth) else {
            return []
        }

        var days: [Date] = []
        for i in 0..<42 { // 6 weeks * 7 days
            if let date = calendar.date(byAdding: .day, value: i, to: startDate) {
                days.append(date)
            }
        }

        return days
    }

    private func previousMonth() {
        if let newMonth = calendar.date(byAdding: .month, value: -1, to: currentMonth) {
            currentMonth = newMonth
        }
    }

    private func nextMonth() {
        if let newMonth = calendar.date(byAdding: .month, value: 1, to: currentMonth) {
            currentMonth = newMonth
        }
    }

    private func shareStreaks() {
        // Implement sharing functionality
        let text = "I've been using the app for \(usageManager.history.totalDays) days with a current streak of \(usageManager.history.currentStreak) days!"

        let activityVC = UIActivityViewController(activityItems: [text], applicationActivities: nil)

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityVC, animated: true)
        }
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: SerenityTheme.Spacing.sm) {
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(color)

            Text(value)
                .font(SerenityTheme.Typography.headline)
                .fontWeight(.bold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            Text(title)
                .font(SerenityTheme.Typography.caption1)
                .foregroundColor(SerenityTheme.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(SerenityTheme.Spacing.md)
        .serenityCardStyle()
    }
}

struct CalendarDayView: View {
    let date: Date
    let hasUsage: Bool
    let isCurrentMonth: Bool

    private var dayNumber: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"
        return formatter.string(from: date)
    }

    private var isToday: Bool {
        Calendar.current.isDateInToday(date)
    }

    var body: some View {
        ZStack {
            Circle()
                .fill(backgroundColor)
                .frame(width: 36, height: 36)

            Text(dayNumber)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(textColor)
        }
        .opacity(isCurrentMonth ? 1.0 : 0.3)
    }

    private var backgroundColor: Color {
        if hasUsage {
            return .white
        } else if isToday {
            return .white.opacity(0.3)
        } else {
            return .clear
        }
    }

    private var textColor: Color {
        if hasUsage {
            return .blue
        } else {
            return .white
        }
    }
}

#Preview {
    ProfileView()
}
