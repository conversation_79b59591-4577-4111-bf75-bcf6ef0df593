//
//  WelcomeView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct WelcomeView: View {
    @State private var showingAuthView = false
    
    var body: some View {
        ZStack {
            // Background Ocean Image
            Image("ocean_background")
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .clipped()
                .ignoresSafeArea()
            
            // Dark overlay for better text readability
            Color.black.opacity(0.3)
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                Spacer()
                
                // App Logo and Title
                VStack(spacing: SerenityTheme.Spacing.lg) {
                    // App Icon/Logo placeholder
                    Circle()
                        .fill(SerenityTheme.Colors.primary.opacity(0.8))
                        .frame(width: 80, height: 80)
                        .overlay(
                            Image(systemName: "wind")
                                .font(.system(size: 40, weight: .light))
                                .foregroundColor(.white)
                        )
                    
                    // App Title
                    Text("Serenity")
                        .font(SerenityTheme.Typography.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                }
                
                Spacer()
                    .frame(height: SerenityTheme.Spacing.xxl)
                
                // Tagline
                VStack(spacing: SerenityTheme.Spacing.md) {
                    Text("Breathe In, Peace Out")
                        .font(SerenityTheme.Typography.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                    
                    Text("Find your calm with guided breathing exercises.")
                        .font(SerenityTheme.Typography.body)
                        .foregroundColor(.white.opacity(0.9))
                        .multilineTextAlignment(.center)
                        .shadow(color: .black.opacity(0.5), radius: 1, x: 0, y: 1)
                        .padding(.horizontal, SerenityTheme.Spacing.xl)
                }
                
                Spacer()
                    .frame(height: SerenityTheme.Spacing.xxl * 2)
                
                // Get Started Button
                Button(action: {
                    showingAuthView = true
                }) {
                    HStack {
                        Text("Get Started")
                            .font(SerenityTheme.Typography.headline)
                            .fontWeight(.semibold)
                        
                        Image(systemName: "arrow.right")
                            .font(.system(size: 16, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, SerenityTheme.Spacing.md + 4)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                SerenityTheme.Colors.primary,
                                SerenityTheme.Colors.primaryDark
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(SerenityTheme.CornerRadius.medium)
                    .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
                }
                .padding(.horizontal, SerenityTheme.Spacing.xl)
                
                Spacer()
                    .frame(height: SerenityTheme.Spacing.xxl)
            }
        }
        .sheet(isPresented: $showingAuthView) {
            AuthenticationView()
        }
    }
}

#Preview {
    WelcomeView()
}
