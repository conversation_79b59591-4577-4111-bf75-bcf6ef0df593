//
//  BreathingExerciseView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct BreathingExerciseView: View {
    let technique: BreathingTechnique
    @StateObject private var breathingTimer = BreathingTimer()
    @Environment(\.dismiss) private var dismiss
    @State private var showingInstructions = true
    @State private var currentInstructionIndex = 0
    
    var body: some View {
        Group {
            if showingInstructions {
                instructionsView
            } else {
                exerciseView
            }
        }
        .serenityOceanBackground()
        .onAppear {
            breathingTimer.setup(with: technique.breathingPattern)
        }
    }
    
    private var instructionsView: some View {
        VStack(spacing: SerenityTheme.Spacing.xl) {
            // Header
            HStack {
                Button("Cancel") {
                    dismiss()
                }
                .foregroundColor(SerenityTheme.Colors.textSecondary)
                
                Spacer()
                
                Text("instructions".localized())
                    .font(SerenityTheme.Typography.headline)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)
                
                Spacer()
                
                // Invisible button for balance
                Button("") {}
                    .opacity(0)
            }
            .padding(.horizontal, SerenityTheme.Spacing.lg)
            .padding(.top, SerenityTheme.Spacing.lg)
            
            Spacer()
            
            // Technique Info
            VStack(spacing: SerenityTheme.Spacing.lg) {
                // Technique Image
                Circle()
                    .fill(technique.category.color.opacity(0.3))
                    .frame(width: 120, height: 120)
                    .overlay(
                        Image(systemName: "wind")
                            .font(.system(size: 40))
                            .foregroundColor(technique.category.color)
                    )
                
                VStack(spacing: SerenityTheme.Spacing.md) {
                    Text(technique.name)
                        .font(SerenityTheme.Typography.title1)
                        .fontWeight(.bold)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                        .multilineTextAlignment(.center)
                    
                    Text(technique.description)
                        .font(SerenityTheme.Typography.body)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, SerenityTheme.Spacing.xl)
                }
            }
            
            // Instructions Steps
            VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
                Text("instructions".localized())
                    .font(SerenityTheme.Typography.title3)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)
                
                VStack(alignment: .leading, spacing: SerenityTheme.Spacing.sm) {
                    ForEach(Array(technique.instructions.enumerated()), id: \.offset) { index, instruction in
                        HStack(alignment: .top, spacing: SerenityTheme.Spacing.sm) {
                            Text("\(index + 1).")
                                .font(SerenityTheme.Typography.callout)
                                .fontWeight(.medium)
                                .foregroundColor(SerenityTheme.Colors.primary)
                                .frame(width: 20, alignment: .leading)
                            
                            Text(instruction)
                                .font(SerenityTheme.Typography.callout)
                                .foregroundColor(SerenityTheme.Colors.textSecondary)
                        }
                    }
                }
            }
            .padding(.horizontal, SerenityTheme.Spacing.xl)
            
            Spacer()
            
            // Start Button
            Button(action: {
                showingInstructions = false
                breathingTimer.start()
            }) {
                Text("start_practice".localized())
                    .font(SerenityTheme.Typography.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, SerenityTheme.Spacing.md + 4)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                SerenityTheme.Colors.primary,
                                SerenityTheme.Colors.primaryDark
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(SerenityTheme.CornerRadius.medium)
            }
            .padding(.horizontal, SerenityTheme.Spacing.xl)
            .padding(.bottom, SerenityTheme.Spacing.xl)
        }
    }
    
    private var exerciseView: some View {
        VStack(spacing: SerenityTheme.Spacing.xl) {
            // Header
            HStack {
                Button(action: {
                    breathingTimer.pause()
                    showingInstructions = true
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                }
                
                Spacer()
                
                VStack(spacing: 2) {
                    Text(technique.name)
                        .font(SerenityTheme.Typography.headline)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                    
                    Text("cycle_of".localized().replacingOccurrences(of: "%d", with: "\(breathingTimer.currentCycle)").replacingOccurrences(of: "%d", with: "\(technique.breathingPattern.cycles)"))
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                }
                
                Spacer()
                
                Button(action: {
                    breathingTimer.isRunning ? breathingTimer.pause() : breathingTimer.resume()
                }) {
                    Image(systemName: breathingTimer.isRunning ? "pause.fill" : "play.fill")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                }
            }
            .padding(.horizontal, SerenityTheme.Spacing.lg)
            .padding(.top, SerenityTheme.Spacing.lg)
            
            Spacer()
            
            // Breathing Circle
            BreathingCircle(
                phase: breathingTimer.currentPhase,
                progress: breathingTimer.phaseProgress
            )
            
            // Phase Instruction
            VStack(spacing: SerenityTheme.Spacing.sm) {
                Text(breathingTimer.currentPhase.instruction)
                    .font(SerenityTheme.Typography.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)
                
                Text("\(Int(breathingTimer.timeRemaining))s")
                    .font(SerenityTheme.Typography.title3)
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
            }
            
            Spacer()
            
            // Progress Bar
            VStack(spacing: SerenityTheme.Spacing.sm) {
                ProgressView(value: breathingTimer.overallProgress)
                    .progressViewStyle(LinearProgressViewStyle(tint: SerenityTheme.Colors.primary))
                    .scaleEffect(x: 1, y: 2, anchor: .center)
                
                Text("percent_complete".localized().replacingOccurrences(of: "%d", with: "\(Int(breathingTimer.overallProgress * 100))"))
                    .font(SerenityTheme.Typography.caption1)
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
            }
            .padding(.horizontal, SerenityTheme.Spacing.xl)
            .padding(.bottom, SerenityTheme.Spacing.xl)
        }
        .onReceive(breathingTimer.$isCompleted) { isCompleted in
            if isCompleted {
                // Show completion view or dismiss
                dismiss()
            }
        }
    }
}

struct BreathingCircle: View {
    let phase: BreathingPhase
    let progress: Double
    
    private var circleScale: CGFloat {
        switch phase {
        case .inhale:
            return 0.6 + (0.4 * progress)
        case .hold, .pause:
            return 1.0
        case .exhale:
            return 1.0 - (0.4 * progress)
        }
    }
    
    private var circleColor: Color {
        switch phase {
        case .inhale:
            return SerenityTheme.Colors.breathingInhale
        case .hold:
            return SerenityTheme.Colors.breathingHold
        case .exhale:
            return SerenityTheme.Colors.breathingExhale
        case .pause:
            return SerenityTheme.Colors.textTertiary
        }
    }
    
    var body: some View {
        ZStack {
            // Outer ring
            Circle()
                .stroke(circleColor.opacity(0.3), lineWidth: 4)
                .frame(width: 200, height: 200)
            
            // Inner circle
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            circleColor.opacity(0.8),
                            circleColor.opacity(0.4)
                        ]),
                        center: .center,
                        startRadius: 20,
                        endRadius: 100
                    )
                )
                .frame(width: 200, height: 200)
                .scaleEffect(circleScale)
                .animation(.easeInOut(duration: 0.5), value: circleScale)
            
            // Center icon
            Image(systemName: phase.iconName)
                .font(.system(size: 30, weight: .light))
                .foregroundColor(.white)
        }
    }
}

#Preview {
    BreathingExerciseView(technique: BreathingTechnique.breathing478)
}
