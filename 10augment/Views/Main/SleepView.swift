//
//  SleepView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct SleepView: View {
    @State private var selectedTechnique: BreathingTechnique?
    
    private var sleepTechniques: [BreathingTechnique] {
        BreathingTechnique.allTechniques.filter { $0.category == .insomnia }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                    VStack(spacing: SerenityTheme.Spacing.xl) {
                        // Header
                        headerSection
                        
                        // Sleep Techniques
                        sleepTechniquesSection
                        
                        // Sleep Tips
                        sleepTipsSection
                    }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationBarHidden(true)
        }
        .sheet(item: $selectedTechnique) { technique in
            BreathingExerciseView(technique: technique)
        }
    }
    
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Sleep")
                .font(SerenityTheme.Typography.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            Text("Breathing exercises to help you fall asleep peacefully")
                .font(SerenityTheme.Typography.body)
                .foregroundColor(SerenityTheme.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(.top, SerenityTheme.Spacing.lg)
    }
    
    private var sleepTechniquesSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Sleep Techniques")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            VStack(spacing: SerenityTheme.Spacing.sm) {
                ForEach(sleepTechniques) { technique in
                    BreathingTechniqueCard(technique: technique) {
                        selectedTechnique = technique
                    }
                }
            }
        }
    }
    
    private var sleepTipsSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Sleep Tips")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            VStack(spacing: SerenityTheme.Spacing.sm) {
                SleepTipCard(
                    icon: "moon.stars",
                    title: "Create a Bedtime Routine",
                    description: "Practice breathing exercises 30 minutes before bed"
                )
                
                SleepTipCard(
                    icon: "thermometer",
                    title: "Cool Environment",
                    description: "Keep your bedroom between 60-67°F for optimal sleep"
                )
                
                SleepTipCard(
                    icon: "iphone.slash",
                    title: "Digital Detox",
                    description: "Avoid screens 1 hour before bedtime"
                )
            }
        }
    }
}

struct SleepTipCard: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: SerenityTheme.Spacing.md) {
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(SerenityTheme.Colors.insomnia)
                .frame(width: 40, height: 40)
                .background(SerenityTheme.Colors.insomnia.opacity(0.2))
                .clipShape(Circle())
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(SerenityTheme.Typography.callout)
                    .fontWeight(.medium)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)
                
                Text(description)
                    .font(SerenityTheme.Typography.caption1)
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
            }
            
            Spacer()
        }
        .padding(SerenityTheme.Spacing.md)
        .serenityCardStyle()
    }
}

#Preview {
    SleepView()
}
