//
//  HomeView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct HomeView: View {
    @StateObject private var authService = AuthenticationService()
    @State private var showingBreathingExercise = false
    @State private var selectedTechnique: BreathingTechnique?
    
    var body: some View {
        NavigationView {
            ZStack {
                SerenityTheme.Colors.background
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: SerenityTheme.Spacing.xl) {
                        // Header
                        headerSection
                        
                        // Quick Start Section
                        quickStartSection
                        
                        // Today's Recommendation
                        recommendationSection
                        
                        // Recent Sessions
                        recentSessionsSection
                        
                        // Progress Overview
                        progressSection
                    }
                    .padding(.horizontal, SerenityTheme.Spacing.lg)
                }
            }
            .navigationBarHidden(true)
        }
        .sheet(item: $selectedTechnique) { technique in
            BreathingExerciseView(technique: technique)
        }
    }
    
    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Good \(timeOfDay)")
                    .font(SerenityTheme.Typography.callout)
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
                
                Text(authService.currentUser?.name ?? "Welcome")
                    .font(SerenityTheme.Typography.title1)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)
            }
            
            Spacer()
            
            // Profile Button
            Button(action: {}) {
                AsyncImage(url: URL(string: authService.currentUser?.profileImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Circle()
                        .fill(SerenityTheme.Colors.primary)
                        .overlay(
                            Text(authService.currentUser?.name.prefix(1).uppercased() ?? "U")
                                .font(SerenityTheme.Typography.headline)
                                .foregroundColor(.white)
                        )
                }
                .frame(width: 40, height: 40)
                .clipShape(Circle())
            }
        }
        .padding(.top, SerenityTheme.Spacing.lg)
    }
    
    private var quickStartSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Quick Start")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: SerenityTheme.Spacing.md) {
                    ForEach(BreathingTechnique.allTechniques.prefix(3)) { technique in
                        QuickStartCard(technique: technique) {
                            selectedTechnique = technique
                        }
                    }
                }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
            }
            .padding(.horizontal, -SerenityTheme.Spacing.lg)
        }
    }
    
    private var recommendationSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Today's Recommendation")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            RecommendationCard(technique: BreathingTechnique.breathing478) {
                selectedTechnique = BreathingTechnique.breathing478
            }
        }
    }
    
    private var recentSessionsSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            HStack {
                Text("Recent Sessions")
                    .font(SerenityTheme.Typography.title3)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)
                
                Spacer()
                
                Button("See All") {
                    // Navigate to sessions history
                }
                .font(SerenityTheme.Typography.callout)
                .foregroundColor(SerenityTheme.Colors.primary)
            }
            
            VStack(spacing: SerenityTheme.Spacing.sm) {
                SessionRow(
                    techniqueName: "4-7-8 Breathing",
                    duration: "5:30",
                    date: "Today",
                    completion: 100
                )
                
                SessionRow(
                    techniqueName: "Box Breathing",
                    duration: "8:00",
                    date: "Yesterday",
                    completion: 85
                )
                
                SessionRow(
                    techniqueName: "Equal Breathing",
                    duration: "3:45",
                    date: "2 days ago",
                    completion: 100
                )
            }
        }
    }
    
    private var progressSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Your Progress")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            HStack(spacing: SerenityTheme.Spacing.md) {
                ProgressCard(
                    title: "Streak",
                    value: "7",
                    subtitle: "days",
                    color: SerenityTheme.Colors.success
                )
                
                ProgressCard(
                    title: "Total Time",
                    value: "2h 45m",
                    subtitle: "this week",
                    color: SerenityTheme.Colors.primary
                )
            }
        }
    }
    
    private var timeOfDay: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12:
            return "Morning"
        case 12..<17:
            return "Afternoon"
        case 17..<21:
            return "Evening"
        default:
            return "Night"
        }
    }
}

struct QuickStartCard: View {
    let technique: BreathingTechnique
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: SerenityTheme.Spacing.sm) {
                // Technique Image
                RoundedRectangle(cornerRadius: SerenityTheme.CornerRadius.small)
                    .fill(technique.category.color.opacity(0.3))
                    .frame(height: 80)
                    .overlay(
                        Image(systemName: "wind")
                            .font(.system(size: 24))
                            .foregroundColor(technique.category.color)
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(technique.name)
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.medium)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                        .lineLimit(2)
                    
                    Text("\(Int(technique.duration / 60)) min")
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                }
            }
            .frame(width: 140)
            .padding(SerenityTheme.Spacing.md)
            .serenityCardStyle()
        }
    }
}

struct RecommendationCard: View {
    let technique: BreathingTechnique
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: SerenityTheme.Spacing.md) {
                // Technique Image
                RoundedRectangle(cornerRadius: SerenityTheme.CornerRadius.small)
                    .fill(technique.category.color.opacity(0.3))
                    .frame(width: 80, height: 80)
                    .overlay(
                        Image(systemName: "moon.stars")
                            .font(.system(size: 24))
                            .foregroundColor(technique.category.color)
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(technique.name)
                        .font(SerenityTheme.Typography.headline)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                    
                    Text(technique.description)
                        .font(SerenityTheme.Typography.callout)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                        .lineLimit(2)
                    
                    Text("\(Int(technique.duration / 60)) minutes")
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.primary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(SerenityTheme.Colors.textTertiary)
            }
            .padding(SerenityTheme.Spacing.md)
            .serenityCardStyle()
        }
    }
}

struct SessionRow: View {
    let techniqueName: String
    let duration: String
    let date: String
    let completion: Int
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(techniqueName)
                    .font(SerenityTheme.Typography.callout)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)
                
                Text("\(duration) • \(date)")
                    .font(SerenityTheme.Typography.caption1)
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
            }
            
            Spacer()
            
            Text("\(completion)%")
                .font(SerenityTheme.Typography.caption1)
                .foregroundColor(completion == 100 ? SerenityTheme.Colors.success : SerenityTheme.Colors.textSecondary)
        }
        .padding(.vertical, SerenityTheme.Spacing.sm)
    }
}

struct ProgressCard: View {
    let title: String
    let value: String
    let subtitle: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(SerenityTheme.Typography.caption1)
                .foregroundColor(SerenityTheme.Colors.textSecondary)
            
            Text(value)
                .font(SerenityTheme.Typography.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(subtitle)
                .font(SerenityTheme.Typography.caption2)
                .foregroundColor(SerenityTheme.Colors.textTertiary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(SerenityTheme.Spacing.md)
        .serenityCardStyle()
    }
}

#Preview {
    HomeView()
}
