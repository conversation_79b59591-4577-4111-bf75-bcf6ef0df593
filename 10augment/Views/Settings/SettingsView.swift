//
//  SettingsView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var notificationsEnabled = true
    @State private var soundEnabled = true
    @State private var vibrationEnabled = true
    @State private var selectedTheme: AppTheme = .system
    @State private var selectedLanguage = "English"
    
    private let languages = ["English", "Spanish", "French", "German", "Japanese", "Chinese"]
    
    var body: some View {
        NavigationView {
            ScrollView {
                    VStack(spacing: SerenityTheme.Spacing.xl) {
                        // Notifications Section
                        notificationsSection
                        
                        // Audio Section
                        audioSection
                        
                        // Appearance Section
                        appearanceSection
                        
                        // Language Section
                        languageSection
                    }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
                .padding(.vertical, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(SerenityTheme.Colors.primary)
                }
            }
        }
    }
    
    private var notificationsSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Notifications")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            VStack(spacing: SerenityTheme.Spacing.sm) {
                SettingsToggleRow(
                    title: "Push Notifications",
                    subtitle: "Receive reminders and updates",
                    isOn: $notificationsEnabled
                )
            }
        }
    }
    
    private var audioSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Audio")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            VStack(spacing: SerenityTheme.Spacing.sm) {
                SettingsToggleRow(
                    title: "Sound Effects",
                    subtitle: "Play sounds during exercises",
                    isOn: $soundEnabled
                )
                
                SettingsToggleRow(
                    title: "Vibration",
                    subtitle: "Haptic feedback for breathing cues",
                    isOn: $vibrationEnabled
                )
            }
        }
    }
    
    private var appearanceSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Appearance")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            VStack(spacing: SerenityTheme.Spacing.sm) {
                ForEach(AppTheme.allCases, id: \.self) { theme in
                    SettingsSelectionRow(
                        title: theme.displayName,
                        isSelected: selectedTheme == theme,
                        action: { selectedTheme = theme }
                    )
                }
            }
        }
    }
    
    private var languageSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Language")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            VStack(spacing: SerenityTheme.Spacing.sm) {
                ForEach(languages, id: \.self) { language in
                    SettingsSelectionRow(
                        title: language,
                        isSelected: selectedLanguage == language,
                        action: { selectedLanguage = language }
                    )
                }
            }
        }
    }
}

struct SettingsToggleRow: View {
    let title: String
    let subtitle: String
    @Binding var isOn: Bool
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(SerenityTheme.Typography.callout)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)
                
                Text(subtitle)
                    .font(SerenityTheme.Typography.caption1)
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
            }
            
            Spacer()
            
            Toggle("", isOn: $isOn)
                .toggleStyle(SwitchToggleStyle(tint: SerenityTheme.Colors.primary))
        }
        .padding(SerenityTheme.Spacing.md)
        .serenityCardStyle()
    }
}

struct SettingsSelectionRow: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Text(title)
                    .font(SerenityTheme.Typography.callout)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(SerenityTheme.Colors.primary)
                }
            }
            .padding(SerenityTheme.Spacing.md)
            .serenityCardStyle()
        }
    }
}

#Preview {
    SettingsView()
}
