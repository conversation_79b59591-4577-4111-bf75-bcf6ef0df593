// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		4D4322622E2DB53700351225 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 4D4322542E2DB53700351225 /* Localizable.strings */; };
		4D4322632E2DB53700351225 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 4D4322572E2DB53700351225 /* Localizable.strings */; };
		4D4322642E2DB53700351225 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 4D43225A2E2DB53700351225 /* Localizable.strings */; };
		4D4322652E2DB53700351225 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 4D43225D2E2DB53700351225 /* Localizable.strings */; };
		4D4322662E2DB53700351225 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 4D4322602E2DB53700351225 /* Localizable.strings */; };
		4D4322682E2DB56600351225 /* LocalizationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D4322672E2DB56600351225 /* LocalizationManager.swift */; };
		4D43226A2E2DB57800351225 /* LanguageSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D4322692E2DB57800351225 /* LanguageSelectionView.swift */; };
		4D5B6C1F2E2B5ED200B0E4B2 /* SerenityApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5B6C1E2E2B5ED200B0E4B2 /* SerenityApp.swift */; };
		4D5B6C252E2D557000B0E4B2 /* ContactUsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5B6C212E2D557000B0E4B2 /* ContactUsView.swift */; };
		4D5B6C262E2D557000B0E4B2 /* HelpCenterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5B6C222E2D557000B0E4B2 /* HelpCenterView.swift */; };
		4D5B6C272E2D557000B0E4B2 /* TermsOfServiceView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5B6C232E2D557000B0E4B2 /* TermsOfServiceView.swift */; };
		4D5B6C282E2D557000B0E4B2 /* PrivacyPolicyView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5B6C242E2D557000B0E4B2 /* PrivacyPolicyView.swift */; };
		4D5B6C2A2E2D58D300B0E4B2 /* NotificationSettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5B6C292E2D58D300B0E4B2 /* NotificationSettingsView.swift */; };
		4D5B6C2C2E2D590E00B0E4B2 /* NotificationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5B6C2B2E2D590E00B0E4B2 /* NotificationManager.swift */; };
		4D9CF7DE2E2B073200024E60 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7DD2E2B073200024E60 /* ContentView.swift */; };
		4D9CF7E02E2B073400024E60 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 4D9CF7DF2E2B073400024E60 /* Assets.xcassets */; };
		4D9CF7E32E2B073400024E60 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 4D9CF7E22E2B073400024E60 /* Preview Assets.xcassets */; };
		4D9CF80C2E2B0FF800024E60 /* SubscriptionService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7EA2E2B0FF800024E60 /* SubscriptionService.swift */; };
		4D9CF80D2E2B0FF800024E60 /* AuthenticationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7EB2E2B0FF800024E60 /* AuthenticationService.swift */; };
		4D9CF80E2E2B0FF800024E60 /* PersistenceController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7EC2E2B0FF800024E60 /* PersistenceController.swift */; };
		4D9CF80F2E2B0FF800024E60 /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7EF2E2B0FF800024E60 /* SettingsView.swift */; };
		4D9CF8102E2B0FF800024E60 /* BreathingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7F12E2B0FF800024E60 /* BreathingView.swift */; };
		4D9CF8112E2B0FF800024E60 /* BreathingExerciseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7F22E2B0FF800024E60 /* BreathingExerciseView.swift */; };
		4D9CF8122E2B0FF800024E60 /* SubscriptionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7F42E2B0FF800024E60 /* SubscriptionView.swift */; };
		4D9CF8132E2B0FF800024E60 /* ProfileView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7F62E2B0FF800024E60 /* ProfileView.swift */; };
		4D9CF8142E2B0FF800024E60 /* MainTabView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7F82E2B0FF800024E60 /* MainTabView.swift */; };
		4D9CF8152E2B0FF800024E60 /* MeditateView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7F92E2B0FF800024E60 /* MeditateView.swift */; };
		4D9CF8162E2B0FF800024E60 /* MoreView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7FA2E2B0FF800024E60 /* MoreView.swift */; };
		4D9CF8172E2B0FF800024E60 /* HomeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7FB2E2B0FF800024E60 /* HomeView.swift */; };
		4D9CF8182E2B0FF800024E60 /* SleepView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7FC2E2B0FF800024E60 /* SleepView.swift */; };
		4D9CF8192E2B0FF800024E60 /* AuthenticationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF7FE2E2B0FF800024E60 /* AuthenticationView.swift */; };
		4D9CF81A2E2B0FF800024E60 /* WelcomeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF8002E2B0FF800024E60 /* WelcomeView.swift */; };
		4D9CF81B2E2B0FF800024E60 /* Session.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF8022E2B0FF800024E60 /* Session.swift */; };
		4D9CF81C2E2B0FF800024E60 /* BreathingTechnique.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF8032E2B0FF800024E60 /* BreathingTechnique.swift */; };
		4D9CF81D2E2B0FF800024E60 /* User.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF8042E2B0FF800024E60 /* User.swift */; };
		4D9CF81E2E2B0FF800024E60 /* BreathingTimer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF8062E2B0FF800024E60 /* BreathingTimer.swift */; };
		4D9CF81F2E2B0FF800024E60 /* SerenityTheme.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF8082E2B0FF800024E60 /* SerenityTheme.swift */; };
		4D9CF8202E2B0FF800024E60 /* SerenityDataModel.xcdatamodeld in Sources */ = {isa = PBXBuildFile; fileRef = 4D9CF8092E2B0FF800024E60 /* SerenityDataModel.xcdatamodeld */; };
		4D9CF8212E2B0FF800024E60 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 4D9CF80B2E2B0FF800024E60 /* Localizable.strings */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		4D4322552E2DB53700351225 /* fr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fr; path = Localizable.strings; sourceTree = "<group>"; };
		4D4322582E2DB53700351225 /* es */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = es; path = Localizable.strings; sourceTree = "<group>"; };
		4D43225B2E2DB53700351225 /* zh */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = zh; path = Localizable.strings; sourceTree = "<group>"; };
		4D43225E2E2DB53700351225 /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = Localizable.strings; sourceTree = "<group>"; };
		4D4322612E2DB53700351225 /* de */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = de; path = Localizable.strings; sourceTree = "<group>"; };
		4D4322672E2DB56600351225 /* LocalizationManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LocalizationManager.swift; sourceTree = "<group>"; };
		4D4322692E2DB57800351225 /* LanguageSelectionView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LanguageSelectionView.swift; sourceTree = "<group>"; };
		4D5B6C1E2E2B5ED200B0E4B2 /* SerenityApp.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SerenityApp.swift; sourceTree = "<group>"; };
		4D5B6C212E2D557000B0E4B2 /* ContactUsView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ContactUsView.swift; sourceTree = "<group>"; };
		4D5B6C222E2D557000B0E4B2 /* HelpCenterView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HelpCenterView.swift; sourceTree = "<group>"; };
		4D5B6C232E2D557000B0E4B2 /* TermsOfServiceView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TermsOfServiceView.swift; sourceTree = "<group>"; };
		4D5B6C242E2D557000B0E4B2 /* PrivacyPolicyView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PrivacyPolicyView.swift; sourceTree = "<group>"; };
		4D5B6C292E2D58D300B0E4B2 /* NotificationSettingsView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotificationSettingsView.swift; sourceTree = "<group>"; };
		4D5B6C2B2E2D590E00B0E4B2 /* NotificationManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotificationManager.swift; sourceTree = "<group>"; };
		4D9CF7D82E2B073200024E60 /* 10augment.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = 10augment.app; sourceTree = BUILT_PRODUCTS_DIR; };
		4D9CF7DD2E2B073200024E60 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		4D9CF7DF2E2B073400024E60 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		4D9CF7E22E2B073400024E60 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		4D9CF7EA2E2B0FF800024E60 /* SubscriptionService.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SubscriptionService.swift; sourceTree = "<group>"; };
		4D9CF7EB2E2B0FF800024E60 /* AuthenticationService.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AuthenticationService.swift; sourceTree = "<group>"; };
		4D9CF7EC2E2B0FF800024E60 /* PersistenceController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PersistenceController.swift; sourceTree = "<group>"; };
		4D9CF7EF2E2B0FF800024E60 /* SettingsView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		4D9CF7F12E2B0FF800024E60 /* BreathingView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BreathingView.swift; sourceTree = "<group>"; };
		4D9CF7F22E2B0FF800024E60 /* BreathingExerciseView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BreathingExerciseView.swift; sourceTree = "<group>"; };
		4D9CF7F42E2B0FF800024E60 /* SubscriptionView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SubscriptionView.swift; sourceTree = "<group>"; };
		4D9CF7F62E2B0FF800024E60 /* ProfileView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProfileView.swift; sourceTree = "<group>"; };
		4D9CF7F82E2B0FF800024E60 /* MainTabView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MainTabView.swift; sourceTree = "<group>"; };
		4D9CF7F92E2B0FF800024E60 /* MeditateView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MeditateView.swift; sourceTree = "<group>"; };
		4D9CF7FA2E2B0FF800024E60 /* MoreView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MoreView.swift; sourceTree = "<group>"; };
		4D9CF7FB2E2B0FF800024E60 /* HomeView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HomeView.swift; sourceTree = "<group>"; };
		4D9CF7FC2E2B0FF800024E60 /* SleepView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SleepView.swift; sourceTree = "<group>"; };
		4D9CF7FE2E2B0FF800024E60 /* AuthenticationView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AuthenticationView.swift; sourceTree = "<group>"; };
		4D9CF8002E2B0FF800024E60 /* WelcomeView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WelcomeView.swift; sourceTree = "<group>"; };
		4D9CF8022E2B0FF800024E60 /* Session.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Session.swift; sourceTree = "<group>"; };
		4D9CF8032E2B0FF800024E60 /* BreathingTechnique.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BreathingTechnique.swift; sourceTree = "<group>"; };
		4D9CF8042E2B0FF800024E60 /* User.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = User.swift; sourceTree = "<group>"; };
		4D9CF8062E2B0FF800024E60 /* BreathingTimer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BreathingTimer.swift; sourceTree = "<group>"; };
		4D9CF8082E2B0FF800024E60 /* SerenityTheme.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SerenityTheme.swift; sourceTree = "<group>"; };
		4D9CF80A2E2B0FF800024E60 /* SerenityDataModel.xcdatamodel */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcdatamodel; path = SerenityDataModel.xcdatamodel; sourceTree = "<group>"; };
		4D9CF80B2E2B0FF800024E60 /* Localizable.strings */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.strings; path = Localizable.strings; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		4D9CF7D52E2B073200024E60 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4D4322532E2DB53700351225 /* fr.lproj */ = {
			isa = PBXGroup;
			children = (
				4D4322542E2DB53700351225 /* Localizable.strings */,
			);
			path = fr.lproj;
			sourceTree = "<group>";
		};
		4D4322562E2DB53700351225 /* es.lproj */ = {
			isa = PBXGroup;
			children = (
				4D4322572E2DB53700351225 /* Localizable.strings */,
			);
			path = es.lproj;
			sourceTree = "<group>";
		};
		4D4322592E2DB53700351225 /* zh.lproj */ = {
			isa = PBXGroup;
			children = (
				4D43225A2E2DB53700351225 /* Localizable.strings */,
			);
			path = zh.lproj;
			sourceTree = "<group>";
		};
		4D43225C2E2DB53700351225 /* ja.lproj */ = {
			isa = PBXGroup;
			children = (
				4D43225D2E2DB53700351225 /* Localizable.strings */,
			);
			path = ja.lproj;
			sourceTree = "<group>";
		};
		4D43225F2E2DB53700351225 /* de.lproj */ = {
			isa = PBXGroup;
			children = (
				4D4322602E2DB53700351225 /* Localizable.strings */,
			);
			path = de.lproj;
			sourceTree = "<group>";
		};
		4D5B6C202E2D557000B0E4B2 /* Support */ = {
			isa = PBXGroup;
			children = (
				4D5B6C212E2D557000B0E4B2 /* ContactUsView.swift */,
				4D5B6C222E2D557000B0E4B2 /* HelpCenterView.swift */,
				4D5B6C232E2D557000B0E4B2 /* TermsOfServiceView.swift */,
				4D5B6C242E2D557000B0E4B2 /* PrivacyPolicyView.swift */,
			);
			path = Support;
			sourceTree = "<group>";
		};
		4D9CF7CF2E2B073200024E60 = {
			isa = PBXGroup;
			children = (
				4D9CF7DA2E2B073200024E60 /* 10augment */,
				4D9CF7D92E2B073200024E60 /* Products */,
			);
			sourceTree = "<group>";
		};
		4D9CF7D92E2B073200024E60 /* Products */ = {
			isa = PBXGroup;
			children = (
				4D9CF7D82E2B073200024E60 /* 10augment.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		4D9CF7DA2E2B073200024E60 /* 10augment */ = {
			isa = PBXGroup;
			children = (
				4D9CF8012E2B0FF800024E60 /* Models */,
				4D9CF8072E2B0FF800024E60 /* Resources */,
				4D9CF7E92E2B0FF800024E60 /* Services */,
				4D9CF8052E2B0FF800024E60 /* ViewModels */,
				4D9CF7ED2E2B0FF800024E60 /* Views */,
				4D9CF7DD2E2B073200024E60 /* ContentView.swift */,
				4D5B6C1E2E2B5ED200B0E4B2 /* SerenityApp.swift */,
				4D9CF7DF2E2B073400024E60 /* Assets.xcassets */,
				4D9CF7E12E2B073400024E60 /* Preview Content */,
			);
			path = 10augment;
			sourceTree = "<group>";
		};
		4D9CF7E12E2B073400024E60 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				4D9CF7E22E2B073400024E60 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		4D9CF7E92E2B0FF800024E60 /* Services */ = {
			isa = PBXGroup;
			children = (
				4D4322672E2DB56600351225 /* LocalizationManager.swift */,
				4D5B6C2B2E2D590E00B0E4B2 /* NotificationManager.swift */,
				4D9CF7EA2E2B0FF800024E60 /* SubscriptionService.swift */,
				4D9CF7EB2E2B0FF800024E60 /* AuthenticationService.swift */,
				4D9CF7EC2E2B0FF800024E60 /* PersistenceController.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		4D9CF7ED2E2B0FF800024E60 /* Views */ = {
			isa = PBXGroup;
			children = (
				4D5B6C202E2D557000B0E4B2 /* Support */,
				4D9CF7EE2E2B0FF800024E60 /* Settings */,
				4D9CF7F02E2B0FF800024E60 /* Breathing */,
				4D9CF7F32E2B0FF800024E60 /* Subscription */,
				4D9CF7F52E2B0FF800024E60 /* Profile */,
				4D9CF7F72E2B0FF800024E60 /* Main */,
				4D9CF7FD2E2B0FF800024E60 /* Authentication */,
				4D9CF7FF2E2B0FF800024E60 /* Onboarding */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		4D9CF7EE2E2B0FF800024E60 /* Settings */ = {
			isa = PBXGroup;
			children = (
				4D4322692E2DB57800351225 /* LanguageSelectionView.swift */,
				4D5B6C292E2D58D300B0E4B2 /* NotificationSettingsView.swift */,
				4D9CF7EF2E2B0FF800024E60 /* SettingsView.swift */,
			);
			path = Settings;
			sourceTree = "<group>";
		};
		4D9CF7F02E2B0FF800024E60 /* Breathing */ = {
			isa = PBXGroup;
			children = (
				4D9CF7F12E2B0FF800024E60 /* BreathingView.swift */,
				4D9CF7F22E2B0FF800024E60 /* BreathingExerciseView.swift */,
			);
			path = Breathing;
			sourceTree = "<group>";
		};
		4D9CF7F32E2B0FF800024E60 /* Subscription */ = {
			isa = PBXGroup;
			children = (
				4D9CF7F42E2B0FF800024E60 /* SubscriptionView.swift */,
			);
			path = Subscription;
			sourceTree = "<group>";
		};
		4D9CF7F52E2B0FF800024E60 /* Profile */ = {
			isa = PBXGroup;
			children = (
				4D9CF7F62E2B0FF800024E60 /* ProfileView.swift */,
			);
			path = Profile;
			sourceTree = "<group>";
		};
		4D9CF7F72E2B0FF800024E60 /* Main */ = {
			isa = PBXGroup;
			children = (
				4D9CF7F82E2B0FF800024E60 /* MainTabView.swift */,
				4D9CF7F92E2B0FF800024E60 /* MeditateView.swift */,
				4D9CF7FA2E2B0FF800024E60 /* MoreView.swift */,
				4D9CF7FB2E2B0FF800024E60 /* HomeView.swift */,
				4D9CF7FC2E2B0FF800024E60 /* SleepView.swift */,
			);
			path = Main;
			sourceTree = "<group>";
		};
		4D9CF7FD2E2B0FF800024E60 /* Authentication */ = {
			isa = PBXGroup;
			children = (
				4D9CF7FE2E2B0FF800024E60 /* AuthenticationView.swift */,
			);
			path = Authentication;
			sourceTree = "<group>";
		};
		4D9CF7FF2E2B0FF800024E60 /* Onboarding */ = {
			isa = PBXGroup;
			children = (
				4D9CF8002E2B0FF800024E60 /* WelcomeView.swift */,
			);
			path = Onboarding;
			sourceTree = "<group>";
		};
		4D9CF8012E2B0FF800024E60 /* Models */ = {
			isa = PBXGroup;
			children = (
				4D9CF8022E2B0FF800024E60 /* Session.swift */,
				4D9CF8032E2B0FF800024E60 /* BreathingTechnique.swift */,
				4D9CF8042E2B0FF800024E60 /* User.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		4D9CF8052E2B0FF800024E60 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				4D9CF8062E2B0FF800024E60 /* BreathingTimer.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		4D9CF8072E2B0FF800024E60 /* Resources */ = {
			isa = PBXGroup;
			children = (
				4D43225F2E2DB53700351225 /* de.lproj */,
				4D4322562E2DB53700351225 /* es.lproj */,
				4D4322532E2DB53700351225 /* fr.lproj */,
				4D43225C2E2DB53700351225 /* ja.lproj */,
				4D4322592E2DB53700351225 /* zh.lproj */,
				4D9CF8082E2B0FF800024E60 /* SerenityTheme.swift */,
				4D9CF8092E2B0FF800024E60 /* SerenityDataModel.xcdatamodeld */,
				4D9CF80B2E2B0FF800024E60 /* Localizable.strings */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		4D9CF7D72E2B073200024E60 /* 10augment */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4D9CF7E62E2B073400024E60 /* Build configuration list for PBXNativeTarget "10augment" */;
			buildPhases = (
				4D9CF7D42E2B073200024E60 /* Sources */,
				4D9CF7D52E2B073200024E60 /* Frameworks */,
				4D9CF7D62E2B073200024E60 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = 10augment;
			productName = 10augment;
			productReference = 4D9CF7D82E2B073200024E60 /* 10augment.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		4D9CF7D02E2B073200024E60 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1520;
				LastUpgradeCheck = 1520;
				TargetAttributes = {
					4D9CF7D72E2B073200024E60 = {
						CreatedOnToolsVersion = 15.2;
					};
				};
			};
			buildConfigurationList = 4D9CF7D32E2B073200024E60 /* Build configuration list for PBXProject "10augment" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				fr,
				es,
				zh,
				ja,
				de,
			);
			mainGroup = 4D9CF7CF2E2B073200024E60;
			productRefGroup = 4D9CF7D92E2B073200024E60 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				4D9CF7D72E2B073200024E60 /* 10augment */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		4D9CF7D62E2B073200024E60 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4D4322632E2DB53700351225 /* Localizable.strings in Resources */,
				4D4322642E2DB53700351225 /* Localizable.strings in Resources */,
				4D9CF7E32E2B073400024E60 /* Preview Assets.xcassets in Resources */,
				4D4322652E2DB53700351225 /* Localizable.strings in Resources */,
				4D4322622E2DB53700351225 /* Localizable.strings in Resources */,
				4D4322662E2DB53700351225 /* Localizable.strings in Resources */,
				4D9CF8212E2B0FF800024E60 /* Localizable.strings in Resources */,
				4D9CF7E02E2B073400024E60 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		4D9CF7D42E2B073200024E60 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4D9CF7DE2E2B073200024E60 /* ContentView.swift in Sources */,
				4D9CF8172E2B0FF800024E60 /* HomeView.swift in Sources */,
				4D9CF8152E2B0FF800024E60 /* MeditateView.swift in Sources */,
				4D5B6C282E2D557000B0E4B2 /* PrivacyPolicyView.swift in Sources */,
				4D5B6C2A2E2D58D300B0E4B2 /* NotificationSettingsView.swift in Sources */,
				4D43226A2E2DB57800351225 /* LanguageSelectionView.swift in Sources */,
				4D9CF8162E2B0FF800024E60 /* MoreView.swift in Sources */,
				4D9CF81A2E2B0FF800024E60 /* WelcomeView.swift in Sources */,
				4D5B6C252E2D557000B0E4B2 /* ContactUsView.swift in Sources */,
				4D9CF8132E2B0FF800024E60 /* ProfileView.swift in Sources */,
				4D9CF8122E2B0FF800024E60 /* SubscriptionView.swift in Sources */,
				4D4322682E2DB56600351225 /* LocalizationManager.swift in Sources */,
				4D9CF81D2E2B0FF800024E60 /* User.swift in Sources */,
				4D9CF81B2E2B0FF800024E60 /* Session.swift in Sources */,
				4D9CF8102E2B0FF800024E60 /* BreathingView.swift in Sources */,
				4D9CF8182E2B0FF800024E60 /* SleepView.swift in Sources */,
				4D9CF8142E2B0FF800024E60 /* MainTabView.swift in Sources */,
				4D9CF80C2E2B0FF800024E60 /* SubscriptionService.swift in Sources */,
				4D9CF80F2E2B0FF800024E60 /* SettingsView.swift in Sources */,
				4D9CF8112E2B0FF800024E60 /* BreathingExerciseView.swift in Sources */,
				4D5B6C1F2E2B5ED200B0E4B2 /* SerenityApp.swift in Sources */,
				4D9CF81E2E2B0FF800024E60 /* BreathingTimer.swift in Sources */,
				4D9CF80D2E2B0FF800024E60 /* AuthenticationService.swift in Sources */,
				4D5B6C272E2D557000B0E4B2 /* TermsOfServiceView.swift in Sources */,
				4D9CF8192E2B0FF800024E60 /* AuthenticationView.swift in Sources */,
				4D9CF81C2E2B0FF800024E60 /* BreathingTechnique.swift in Sources */,
				4D9CF81F2E2B0FF800024E60 /* SerenityTheme.swift in Sources */,
				4D5B6C262E2D557000B0E4B2 /* HelpCenterView.swift in Sources */,
				4D9CF8202E2B0FF800024E60 /* SerenityDataModel.xcdatamodeld in Sources */,
				4D5B6C2C2E2D590E00B0E4B2 /* NotificationManager.swift in Sources */,
				4D9CF80E2E2B0FF800024E60 /* PersistenceController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		4D4322542E2DB53700351225 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				4D4322552E2DB53700351225 /* fr */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		4D4322572E2DB53700351225 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				4D4322582E2DB53700351225 /* es */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		4D43225A2E2DB53700351225 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				4D43225B2E2DB53700351225 /* zh */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		4D43225D2E2DB53700351225 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				4D43225E2E2DB53700351225 /* ja */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		4D4322602E2DB53700351225 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				4D4322612E2DB53700351225 /* de */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		4D9CF7E42E2B073400024E60 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		4D9CF7E52E2B073400024E60 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		4D9CF7E72E2B073400024E60 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"10augment/Preview Content\"";
				DEVELOPMENT_TEAM = 48C635F3FN;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.serenity.breathingapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		4D9CF7E82E2B073400024E60 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"10augment/Preview Content\"";
				DEVELOPMENT_TEAM = 48C635F3FN;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.serenity.breathingapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		4D9CF7D32E2B073200024E60 /* Build configuration list for PBXProject "10augment" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4D9CF7E42E2B073400024E60 /* Debug */,
				4D9CF7E52E2B073400024E60 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4D9CF7E62E2B073400024E60 /* Build configuration list for PBXNativeTarget "10augment" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4D9CF7E72E2B073400024E60 /* Debug */,
				4D9CF7E82E2B073400024E60 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCVersionGroup section */
		4D9CF8092E2B0FF800024E60 /* SerenityDataModel.xcdatamodeld */ = {
			isa = XCVersionGroup;
			children = (
				4D9CF80A2E2B0FF800024E60 /* SerenityDataModel.xcdatamodel */,
			);
			currentVersion = 4D9CF80A2E2B0FF800024E60 /* SerenityDataModel.xcdatamodel */;
			path = SerenityDataModel.xcdatamodeld;
			sourceTree = "<group>";
			versionGroupType = wrapper.xcdatamodel;
		};
/* End XCVersionGroup section */
	};
	rootObject = 4D9CF7D02E2B073200024E60 /* Project object */;
}
