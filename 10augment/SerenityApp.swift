//
//  SerenityApp.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

@main
struct SerenityApp: App {
    let persistenceController = PersistenceController.shared

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .environmentObject(LocalizationManager.shared)
        }
    }
}
