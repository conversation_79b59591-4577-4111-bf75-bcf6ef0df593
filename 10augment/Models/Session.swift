//
//  Session.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation

struct BreathingSession: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let techniqueId: UUID
    let techniqueName: String
    let startTime: Date
    let endTime: Date?
    let duration: TimeInterval
    let completedCycles: Int
    let targetCycles: Int
    let isCompleted: Bool
    let rating: Int? // 1-5 stars
    let notes: String?
    
    init(userId: UUID, techniqueId: UUID, techniqueName: String, targetCycles: Int) {
        self.id = UUID()
        self.userId = userId
        self.techniqueId = techniqueId
        self.techniqueName = techniqueName
        self.startTime = Date()
        self.endTime = nil
        self.duration = 0
        self.completedCycles = 0
        self.targetCycles = targetCycles
        self.isCompleted = false
        self.rating = nil
        self.notes = nil
    }
    
    var formattedDuration: String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    var completionPercentage: Double {
        guard targetCycles > 0 else { return 0 }
        return Double(completedCycles) / Double(targetCycles)
    }
}

struct SessionStats: Codable {
    let totalSessions: Int
    let totalDuration: TimeInterval
    let averageSessionDuration: TimeInterval
    let completionRate: Double
    let favoriteTime: String // e.g., "Morning", "Evening"
    let streakDays: Int
    let lastSessionDate: Date?
    
    var formattedTotalDuration: String {
        let hours = Int(totalDuration) / 3600
        let minutes = (Int(totalDuration) % 3600) / 60
        
        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
    
    var formattedAverageDuration: String {
        let minutes = Int(averageSessionDuration) / 60
        let seconds = Int(averageSessionDuration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}
