//
//  WelcomeView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct WelcomeView: View {
    @State private var showingAuthView = false
    @State private var navigateToHome = false
    @StateObject private var usageManager = UsageHistoryManager()
    
    var body: some View {
        ZStack {
            // Background Ocean Gradient
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.1, green: 0.3, blue: 0.6),
                    Color(red: 0.2, green: 0.5, blue: 0.8),
                    Color(red: 0.3, green: 0.7, blue: 0.9)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()

            // Dark overlay for better text readability
            Color.black.opacity(0.2)
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                Spacer()
                
                // App Logo and Title
                VStack(spacing: SerenityTheme.Spacing.lg) {
                    // App Icon/Logo placeholder
                    Circle()
                        .fill(SerenityTheme.Colors.primary.opacity(0.8))
                        .frame(width: 80, height: 80)
                        .overlay(
                            Image(systemName: "wind")
                                .font(.system(size: 40, weight: .light))
                                .foregroundColor(.white)
                        )
                    
                    // App Title
                    Text("Serenity")
                        .font(SerenityTheme.Typography.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                }
                
                Spacer()
                    .frame(height: SerenityTheme.Spacing.xxl)
                
                // Tagline
                VStack(spacing: SerenityTheme.Spacing.md) {
                    Text("Breathe In, Peace Out")
                        .font(SerenityTheme.Typography.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                    
                    Text("Find your calm with guided breathing exercises.")
                        .font(SerenityTheme.Typography.body)
                        .foregroundColor(.white.opacity(0.9))
                        .multilineTextAlignment(.center)
                        .shadow(color: .black.opacity(0.5), radius: 1, x: 0, y: 1)
                        .padding(.horizontal, SerenityTheme.Spacing.xl)
                }
                
                Spacer()
            }
        }
        .onAppear {
            // Record daily usage
            usageManager.recordUsage()

            // Auto-navigate to home after 3 seconds
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                navigateToHome = true
            }
        }
        .fullScreenCover(isPresented: $navigateToHome) {
            MainTabView()
        }
    }
}

#Preview {
    WelcomeView()
}
