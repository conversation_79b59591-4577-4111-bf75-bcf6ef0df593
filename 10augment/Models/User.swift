//
//  User.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import SwiftUI

struct User: Codable, Identifiable {
    let id: UUID
    var email: String
    var name: String
    var profileImageURL: String?
    var subscriptionType: SubscriptionType
    var createdAt: Date
    var lastLoginAt: Date
    var preferences: UserPreferences
    var stats: UserStats
    
    init(email: String, name: String) {
        self.id = UUID()
        self.email = email
        self.name = name
        self.subscriptionType = .free
        self.createdAt = Date()
        self.lastLoginAt = Date()
        self.preferences = UserPreferences()
        self.stats = UserStats()
    }
}

struct UserPreferences: Codable {
    var language: String = "en"
    var notificationsEnabled: Bool = true
    var reminderTime: Date?
    var theme: AppTheme = .system
    var soundEnabled: Bool = true
    var vibrationEnabled: Bool = true
}

struct UserStats: Codable {
    var totalSessions: Int = 0
    var totalMinutes: Int = 0
    var currentStreak: Int = 0
    var longestStreak: Int = 0
    var favoriteBreathingTechnique: String?
    var lastSessionDate: Date?
}

enum SubscriptionType: String, Codable, CaseIterable {
    case free = "free"
    case premium = "premium"
    case lifetime = "lifetime"
    
    var displayName: String {
        switch self {
        case .free:
            return "Free"
        case .premium:
            return "Premium"
        case .lifetime:
            return "Lifetime"
        }
    }
}

enum AppTheme: String, Codable, CaseIterable {
    case light = "light"
    case dark = "dark"
    case system = "system"

    var displayName: String {
        switch self {
        case .light:
            return "Light"
        case .dark:
            return "Dark"
        case .system:
            return "System"
        }
    }
}

// MARK: - Usage History Models
struct UsageHistory: Codable {
    var dailyUsage: [String: DailyUsage] = [:]

    mutating func recordUsage(for date: Date) {
        let dateKey = DateFormatter.dateKey.string(from: date)
        if dailyUsage[dateKey] == nil {
            dailyUsage[dateKey] = DailyUsage(date: date)
        }
        dailyUsage[dateKey]?.incrementUsage()
    }

    func getUsage(for date: Date) -> DailyUsage? {
        let dateKey = DateFormatter.dateKey.string(from: date)
        return dailyUsage[dateKey]
    }

    func hasUsage(for date: Date) -> Bool {
        let dateKey = DateFormatter.dateKey.string(from: date)
        return dailyUsage[dateKey] != nil
    }

    var totalDays: Int {
        return dailyUsage.count
    }

    var currentStreak: Int {
        let calendar = Calendar.current
        var streak = 0
        var currentDate = Date()

        while hasUsage(for: currentDate) {
            streak += 1
            currentDate = calendar.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
        }

        return streak
    }

    var longestStreak: Int {
        let sortedDates = dailyUsage.keys.compactMap { DateFormatter.dateKey.date(from: $0) }.sorted()
        guard !sortedDates.isEmpty else { return 0 }

        let calendar = Calendar.current
        var maxStreak = 1
        var currentStreak = 1

        for i in 1..<sortedDates.count {
            let previousDate = sortedDates[i-1]
            let currentDate = sortedDates[i]

            if calendar.isDate(currentDate, inSameDayAs: calendar.date(byAdding: .day, value: 1, to: previousDate) ?? Date()) {
                currentStreak += 1
                maxStreak = max(maxStreak, currentStreak)
            } else {
                currentStreak = 1
            }
        }

        return maxStreak
    }
}

struct DailyUsage: Codable {
    let date: Date
    var sessionCount: Int = 0
    var totalDuration: TimeInterval = 0

    mutating func incrementUsage() {
        sessionCount += 1
    }

    mutating func addDuration(_ duration: TimeInterval) {
        totalDuration += duration
    }
}

// MARK: - Usage History Manager
class UsageHistoryManager: ObservableObject {
    @Published var history = UsageHistory()

    private let userDefaults = UserDefaults.standard
    private let historyKey = "usage_history"

    init() {
        loadHistory()
    }

    func recordUsage() {
        history.recordUsage(for: Date())
        saveHistory()
    }

    private func saveHistory() {
        if let encoded = try? JSONEncoder().encode(history) {
            userDefaults.set(encoded, forKey: historyKey)
        }
    }

    private func loadHistory() {
        if let data = userDefaults.data(forKey: historyKey),
           let decoded = try? JSONDecoder().decode(UsageHistory.self, from: data) {
            history = decoded
        }
    }
}

// MARK: - Date Formatter Extension
extension DateFormatter {
    static let dateKey: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter
    }()

    static let monthYear: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM yyyy"
        return formatter
    }()
}
