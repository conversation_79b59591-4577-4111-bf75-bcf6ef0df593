//
//  LanguageSelectionView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct LanguageSelectionView: View {
    @StateObject private var localizationManager = LocalizationManager.shared
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            List {
                ForEach(Language.allCases) { language in
                    LanguageRow(
                        language: language,
                        isSelected: language == localizationManager.currentLanguage,
                        onSelect: {
                            localizationManager.setLanguage(language)
                            dismiss()
                        }
                    )
                }
            }
            .navigationTitle("language".localized())
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("done".localized()) {
                        dismiss()
                    }
                    .foregroundColor(SerenityTheme.Colors.primary)
                }
            }
            .background(SerenityTheme.Colors.background)
        }
    }
}

struct LanguageRow: View {
    let language: Language
    let isSelected: Bool
    let onSelect: () -> Void

    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: SerenityTheme.Spacing.md) {
                // Flag
                Text(language.flag)
                    .font(.title2)

                // Language name
                VStack(alignment: .leading, spacing: 2) {
                    Text(language.displayName)
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.medium)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)

                    Text(language.nativeName)
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                }

                Spacer()

                // Selection indicator
                if isSelected {
                    Image(systemName: "checkmark")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(SerenityTheme.Colors.primary)
                }
            }
            .padding(.vertical, SerenityTheme.Spacing.sm)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .listRowBackground(
            RoundedRectangle(cornerRadius: SerenityTheme.CornerRadius.medium)
                .fill(SerenityTheme.Colors.cardBackground)
                .padding(.vertical, 2)
        )
    }
}

#Preview {
    LanguageSelectionView()
}
