/* 
  Localizable.strings
  Serenity

  Created by Serenity Team on 2025/7/18.
  
*/

// MARK: - App Name and Branding
"app_name" = "Serenity";
"app_tagline" = "Breathe In, Peace Out";
"app_description" = "Find your calm with guided breathing exercises.";

// MARK: - Welcome and Onboarding
"welcome_title" = "Serenity";
"welcome_tagline" = "Breathe In, Peace Out";
"welcome_description" = "Find your calm with guided breathing exercises.";
"get_started" = "Get Started";

// MARK: - Authentication
"log_into_account" = "Log into your account";
"create_account" = "Create your account";
"email" = "Email";
"password" = "Password";
"name" = "Name";
"continue_with_email" = "Continue with Email";
"continue_with_apple" = "Continue with Apple ID";
"continue_with_google" = "Continue with Google";
"continue_with_tiktok" = "Continue with TikTok";
"continue_with_facebook" = "Continue with Facebook";
"or_continue_with" = "Or continue with";
"already_have_account" = "Already have an account?";
"dont_have_account" = "Don't have an account?";
"sign_in" = "Sign In";
"sign_up" = "Sign Up";
"sign_out" = "Sign Out";
"terms_privacy" = "By continuing, you agree to our Terms of Service and Privacy Policy";

// MARK: - Navigation
"home" = "Home";
"breathe" = "Breathe";
"sleep" = "Sleep";
"meditate" = "Meditate";
"more" = "More";

// MARK: - Home Screen
"good_morning" = "Good Morning";
"good_afternoon" = "Good Afternoon";
"good_evening" = "Good Evening";
"good_night" = "Good Night";
"welcome" = "Welcome";
"quick_start" = "Quick Start";
"todays_recommendation" = "Today's Recommendation";
"recent_sessions" = "Recent Sessions";
"see_all" = "See All";
"your_progress" = "Your Progress";
"streak" = "Streak";
"days" = "days";
"total_time" = "Total Time";
"this_week" = "this week";

// MARK: - Breathing Techniques
"breathing_techniques" = "Breathing Techniques";
"search_breathing_exercises" = "Search breathing exercises";
"all" = "All";
"quick_exercises" = "Quick Exercises";
"advanced" = "Advanced";
"insomnia" = "Insomnia";
"emotion_regulation" = "Emotion Regulation";
"sports" = "Sports";
"minutes" = "minutes";
"min" = "min";
"beginner" = "Beginner";
"intermediate" = "Intermediate";
"advanced_level" = "Advanced";

// MARK: - Breathing Exercise
"instructions" = "Instructions";
"start_practice" = "Start Practice";
"breathe_in" = "Breathe In";
"hold" = "Hold";
"breathe_out" = "Breathe Out";
"pause" = "Pause";
"cycle_of" = "Cycle %d of %d";
"complete" = "Complete";

// MARK: - Sleep
"sleep_description" = "Breathing exercises to help you fall asleep peacefully";
"sleep_techniques" = "Sleep Techniques";
"sleep_tips" = "Sleep Tips";
"create_bedtime_routine" = "Create a Bedtime Routine";
"bedtime_routine_desc" = "Practice breathing exercises 30 minutes before bed";
"cool_environment" = "Cool Environment";
"cool_environment_desc" = "Keep your bedroom between 60-67°F for optimal sleep";
"digital_detox" = "Digital Detox";
"digital_detox_desc" = "Avoid screens 1 hour before bedtime";

// MARK: - Settings
"settings" = "Settings";
"account" = "Account";
"profile" = "Profile";
"subscription" = "Subscription";
"app_settings" = "App Settings";
"notifications" = "Notifications";
"volume" = "Volume";
"theme" = "Theme";
"support" = "Support";
"help_center" = "Help Center";
"contact_us" = "Contact Us";
"terms_of_service" = "Terms of Service";
"privacy_policy" = "Privacy Policy";
"version" = "Version";

// MARK: - Profile
"your_stats" = "Your Stats";
"total_sessions" = "Total Sessions";
"total_minutes" = "Total Minutes";
"current_streak" = "Current Streak";
"longest_streak" = "Longest Streak";
"achievements" = "Achievements";
"recent_activity" = "Recent Activity";

// MARK: - Subscription Types
"free" = "Free";
"premium" = "Premium";
"lifetime" = "Lifetime";

// MARK: - Common Actions
"cancel" = "Cancel";
"done" = "Done";
"save" = "Save";
"edit" = "Edit";
"delete" = "Delete";
"ok" = "OK";
"yes" = "Yes";
"no" = "No";

// MARK: - Time
"today" = "Today";
"yesterday" = "Yesterday";
"days_ago" = "%d days ago";
"hours" = "hours";
"minutes_short" = "min";
"seconds" = "seconds";

// MARK: - Errors
"error" = "Error";
"invalid_email_password" = "Invalid email or password";
"fill_all_fields" = "Please fill in all fields with valid information";
"apple_signin_failed" = "Apple Sign-In failed: %@";
"coming_soon" = "Coming Soon";
"meditation_coming_soon" = "Guided meditation sessions will be available in a future update";

// MARK: - Languages
"language" = "Language";
"english" = "English";
"spanish" = "Español";
"french" = "Français";
"german" = "Deutsch";
"japanese" = "日本語";
"korean" = "한국어";
"chinese" = "中文";

// MARK: - Time of Day
"morning" = "Morning";
"afternoon" = "Afternoon";
"evening" = "Evening";
"night" = "Night";

// MARK: - Breathing Exercise Details
"equal_breathing" = "Equal Breathing";
"equal_breathing_desc" = "Calm your mind with balanced breathing";
"box_breathing" = "Box Breathing";
"box_breathing_desc" = "Structured breathing for stress relief";
"breathing_478" = "4-7-8 Breathing";
"breathing_478_desc" = "Deep relaxation technique for better sleep";
"triangle_breathing" = "Triangle Breathing";
"triangle_breathing_desc" = "Simple pattern for quick relaxation";
"complete_breathing" = "Complete Breathing";
"complete_breathing_desc" = "Advanced technique for deep calm";
"coherent_breathing" = "Coherent Breathing";
"coherent_breathing_desc" = "Harmonize your heart and mind";

// MARK: - Breathing Categories
"basic_breathing" = "Basic Breathing";
"stress_reduction" = "Stress Reduction";
"sleep_preparation" = "Sleep Preparation";
"energy_boost" = "Energy Boost";
"focus_enhancement" = "Focus Enhancement";
"anxiety_relief" = "Anxiety Relief";
"meditate_techniques" = "Meditate Techniques";

// MARK: - Sleep Tips
"create_bedtime_routine" = "Create a Bedtime Routine";
"bedtime_routine_desc" = "Practice breathing exercises 30 minutes before bed";
"cool_environment" = "Cool Environment";
"cool_environment_desc" = "Keep your bedroom between 60-67°F for optimal sleep";
"digital_detox" = "Digital Detox";
"digital_detox_desc" = "Avoid screens 1 hour before bedtime";
"comfortable_mattress" = "Comfortable Mattress";
"comfortable_mattress_desc" = "Invest in a quality mattress and pillows";
"consistent_schedule" = "Consistent Schedule";
"consistent_schedule_desc" = "Go to bed and wake up at the same time daily";
"limit_caffeine" = "Limit Caffeine";
"limit_caffeine_desc" = "Avoid caffeine 6 hours before bedtime";

// MARK: - Meditation Tips
"find_your_posture" = "Find Your Posture";
"posture_desc" = "Sit comfortably with spine straight, shoulders relaxed";
"focus_your_gaze" = "Focus Your Gaze";
"gaze_desc" = "Close eyes or soften gaze downward to reduce distractions";
"observe_thoughts" = "Observe Your Thoughts";
"thoughts_desc" = "Notice thoughts without judgment, gently return to breath";
"start_small" = "Start Small";
"start_small_desc" = "Begin with 5-10 minutes daily, gradually increase duration";
"create_sacred_space" = "Create Sacred Space";
"sacred_space_desc" = "Choose a quiet, comfortable spot for consistent practice";
"practice_self_compassion" = "Practice Self-Compassion";
"self_compassion_desc" = "Be patient with yourself, meditation is a journey not a destination";

// MARK: - Profile & Settings
"sign_in_or_create" = "Sign in or create account";
"free_account" = "Free Account";
"reminders_and_updates" = "Reminders and updates";
"app_appearance" = "App appearance";
"get_help_and_support" = "Get help and support";
"send_us_feedback" = "Send us feedback";
"legal_information" = "Legal information";
"how_we_protect_data" = "How we protect your data";
"sign_out" = "Sign Out";

// MARK: - Achievements
"first_session" = "First Session";
"first_session_desc" = "Complete your first breathing session";
"week_warrior" = "Week Warrior";
"week_warrior_desc" = "Practice for 7 days in a row";
"zen_master" = "Zen Master";
"zen_master_desc" = "Complete 100 sessions";

// MARK: - Notifications
"notification_settings" = "Notification Settings";
"daily_reminders" = "Daily Reminders";
"daily_practice_reminders" = "Daily Practice Reminders";
"get_reminded_practice" = "Get reminded to practice breathing exercises";
"morning_reminder" = "Morning Reminder";
"evening_reminder" = "Evening Reminder";
"weekend_reminders" = "Weekend Reminders";
"include_saturday_sunday" = "Include Saturday and Sunday";
"content_preferences" = "Content Preferences";
"breathing_exercise_reminders" = "Breathing Exercise Reminders";
"suggestions_breathing_techniques" = "Suggestions for breathing techniques";
"meditation_reminders" = "Meditation Reminders";
"mindfulness_meditation_prompts" = "Mindfulness and meditation prompts";
"sleep_reminders" = "Sleep Reminders";
"bedtime_breathing_suggestions" = "Bedtime breathing exercise suggestions";
"streak_reminders" = "Streak Reminders";
"maintain_practice_streak" = "Maintain your practice streak";
"achievement_notifications" = "Achievement Notifications";
"celebrate_milestones" = "Celebrate your milestones";
"app_updates" = "App Updates";
"new_versions_bug_fixes" = "New versions and bug fixes";
"new_features" = "New Features";
"learn_new_breathing_techniques" = "Learn about new breathing techniques";
"tips_insights" = "Tips & Insights";
"wellness_tips_breathing_insights" = "Wellness tips and breathing insights";
"marketing_promotions" = "Marketing & Promotions";
"promotional_offers" = "Promotional Offers";
"special_discounts_premium" = "Special discounts and premium offers";
"newsletter" = "Newsletter";
"monthly_wellness_newsletter" = "Monthly wellness newsletter";
"send_test_notification" = "Send Test Notification";
"reset_to_defaults" = "Reset to Defaults";

// MARK: - Support
"how_can_we_help" = "How can we help you?";
"find_answers_questions" = "Find answers to common questions and learn how to get the most out of Serenity";
"search_help_articles" = "Search help articles";
"getting_started" = "Getting Started";
"troubleshooting" = "Troubleshooting";
"premium_features" = "Premium Features";
"get_in_touch" = "Get in Touch";
"wed_love_hear_from_you" = "We'd love to hear from you. Send us a message and we'll respond as soon as possible.";
"send_us_message" = "Send us a message";
"category" = "Category";
"general_inquiry" = "General Inquiry";
"technical_support" = "Technical Support";
"billing_subscription" = "Billing & Subscription";
"feature_request" = "Feature Request";
"bug_report" = "Bug Report";
"subject" = "Subject";
"brief_description_inquiry" = "Brief description of your inquiry";
"message" = "Message";
"send_message" = "Send Message";
"other_ways_reach_us" = "Other ways to reach us";
"email_support" = "Email Support";
"live_chat" = "Live Chat";
"available_9am_6pm" = "Available 9 AM - 6 PM EST";
"website" = "Website";
"message_sent" = "Message Sent!";
"thank_you_contacting" = "Thank you for contacting us. We'll get back to you within 24 hours.";

// MARK: - Terms and Privacy
"last_updated" = "Last updated";
"please_read_terms_carefully" = "Please read these Terms of Service carefully before using the Serenity app.";
"acceptance_of_terms" = "1. Acceptance of Terms";
"description_of_service" = "2. Description of Service";
"user_accounts" = "3. User Accounts";
"premium_subscription" = "4. Premium Subscription";
"health_disclaimer" = "5. Health Disclaimer";
"intellectual_property" = "6. Intellectual Property";
"privacy" = "7. Privacy";
"prohibited_uses" = "8. Prohibited Uses";
"limitation_liability" = "9. Limitation of Liability";
"changes_to_terms" = "10. Changes to Terms";
"contact_information" = "11. Contact Information";
"your_privacy_important" = "Your privacy is important to us. This Privacy Policy explains how we collect, use, and protect your information when you use Serenity.";
"information_we_collect" = "1. Information We Collect";
"how_we_use_information" = "2. How We Use Your Information";
"information_sharing" = "3. Information Sharing";
"data_security" = "4. Data Security";
"data_retention" = "5. Data Retention";
"your_rights" = "6. Your Rights";
"third_party_services" = "7. Third-Party Services";
"childrens_privacy" = "8. Children's Privacy";
"international_users" = "9. International Users";
"changes_privacy_policy" = "10. Changes to Privacy Policy";

// MARK: - Messages and Alerts
"allow_notifications" = "Allow Notifications";
"notifications_enabled" = "Notifications are enabled";
"enable_receive_reminders" = "Enable to receive reminders and updates";
"notifications_disabled" = "Notifications Disabled";
"enable_notifications_device_settings" = "To receive breathing reminders and updates, please enable notifications in your device settings.";
"open_settings" = "Open Settings";
"choose_preferred_language" = "Choose your preferred language for the app";
"test_notification" = "Test Notification 🧪";
"test_notification_message" = "This is a test notification from Serenity. Your notifications are working!";
"settings_saved_successfully" = "Settings saved successfully";
"login_success" = "Successfully signed in";
"signup_success" = "Account created successfully";
