//
//  MoreView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct MoreView: View {
    @StateObject private var authService = AuthenticationService.shared
    @StateObject private var localizationManager = LocalizationManager.shared
    @State private var showingSettings = false
    @State private var showingProfile = false
    @State private var showingAuth = false
    @State private var showingNotifications = false
    @State private var showingUsageHistory = false

    @State private var showingHelpCenter = false
    @State private var showingContactUs = false
    @State private var showingTerms = false
    @State private var showingPrivacy = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                    VStack(spacing: SerenityTheme.Spacing.xl) {
                        // Header
                        headerSection
                        
                        // Account Section
                        accountSection
                        
                        // App Settings
                        appSettingsSection
                        
                        // Support Section
                        supportSection
                        
                        // Sign Out
                        signOutSection
                    }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingSettings) {
            SettingsView()
        }
        .sheet(isPresented: $showingProfile) {
            ProfileView()
        }
        .sheet(isPresented: $showingAuth) {
            AuthenticationView()
        }
        .sheet(isPresented: $showingNotifications) {
            NotificationSettingsView()
        }
        .sheet(isPresented: $showingUsageHistory) {
            UsageStreaksView()
        }

        .sheet(isPresented: $showingHelpCenter) {
            HelpCenterView()
        }
        .sheet(isPresented: $showingContactUs) {
            ContactUsView()
        }
        .sheet(isPresented: $showingTerms) {
            TermsOfServiceView()
        }
        .sheet(isPresented: $showingPrivacy) {
            PrivacyPolicyView()
        }
    }
    
    private var headerSection: some View {
        HStack {
            Text("Settings")
                .font(SerenityTheme.Typography.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            Spacer()
        }
        .padding(.top, SerenityTheme.Spacing.lg)
    }
    
    private var accountSection: some View {
        VStack(spacing: SerenityTheme.Spacing.sm) {
            if authService.isAuthenticated {
                // Logged in - Show user info
                UserInfoCard(user: authService.currentUser) {
                    showingProfile = true
                }

                SettingsRow(
                    icon: "crown",
                    title: "premium".localized(),
                    subtitle: authService.currentUser?.subscriptionType.displayName ?? "free".localized(),
                    action: {}
                )

                // Usage History - Show streaks when logged in
                SettingsRow(
                    icon: "calendar",
                    title: "Usage History",
                    subtitle: "View your meditation streaks",
                    action: { showingUsageHistory = true }
                )
            } else {
                // Not logged in - Show login option
                SettingsRow(
                    icon: "person.circle",
                    title: "account".localized(),
                    subtitle: "sign_in_or_create".localized(),
                    action: { showingAuth = true }
                )
            }
        }
    }
    
    private var appSettingsSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("app_settings".localized())
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            VStack(spacing: SerenityTheme.Spacing.sm) {
                SettingsRow(
                    icon: "bell",
                    title: "notifications".localized(),
                    subtitle: "reminders_and_updates".localized(),
                    action: { showingNotifications = true }
                )

                SettingsRow(
                    icon: "paintbrush",
                    title: "theme".localized(),
                    subtitle: "app_appearance".localized(),
                    action: { showingSettings = true }
                )
            }
        }
    }
    
    private var supportSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("support".localized())
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            VStack(spacing: SerenityTheme.Spacing.sm) {
                SettingsRow(
                    icon: "questionmark.circle",
                    title: "help_center".localized(),
                    subtitle: "get_help_and_support".localized(),
                    action: { showingHelpCenter = true }
                )

                SettingsRow(
                    icon: "envelope",
                    title: "contact_us".localized(),
                    subtitle: "send_us_feedback".localized(),
                    action: { showingContactUs = true }
                )

                SettingsRow(
                    icon: "doc.text",
                    title: "terms_of_service".localized(),
                    subtitle: "legal_information".localized(),
                    action: { showingTerms = true }
                )

                SettingsRow(
                    icon: "hand.raised",
                    title: "privacy_policy".localized(),
                    subtitle: "how_we_protect_data".localized(),
                    action: { showingPrivacy = true }
                )
            }
        }
    }
    
    private var signOutSection: some View {
        VStack(spacing: SerenityTheme.Spacing.md) {
            Button(action: {
                authService.signOut()
            }) {
                HStack {
                    Image(systemName: "rectangle.portrait.and.arrow.right")
                        .font(.system(size: 16))
                    
                    Text("Sign Out")
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.medium)
                }
                .foregroundColor(SerenityTheme.Colors.error)
                .frame(maxWidth: .infinity)
                .padding(.vertical, SerenityTheme.Spacing.md)
                .background(SerenityTheme.Colors.cardBackground)
                .cornerRadius(SerenityTheme.CornerRadius.small)
            }
            
            Text("Version 1.0.0")
                .font(SerenityTheme.Typography.caption2)
                .foregroundColor(SerenityTheme.Colors.textTertiary)
        }
    }
}

struct SettingsRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: SerenityTheme.Spacing.md) {
                Image(systemName: icon)
                    .font(.system(size: 16))
                    .foregroundColor(SerenityTheme.Colors.primary)
                    .frame(width: 24, height: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(SerenityTheme.Typography.callout)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                    
                    Text(subtitle)
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(SerenityTheme.Colors.textTertiary)
            }
            .padding(SerenityTheme.Spacing.md)
            .serenityCardStyle()
        }
    }
}

struct UserInfoCard: View {
    let user: User?
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: SerenityTheme.Spacing.md) {
                // User Avatar
                AsyncImage(url: URL(string: user?.profileImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Circle()
                        .fill(SerenityTheme.Colors.primary)
                        .overlay(
                            Text(user?.name.prefix(1).uppercased() ?? "U")
                                .font(SerenityTheme.Typography.headline)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        )
                }
                .frame(width: 50, height: 50)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(SerenityTheme.Colors.primary, lineWidth: 2)
                )

                // User Info
                VStack(alignment: .leading, spacing: 4) {
                    Text(user?.name ?? "User")
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.semibold)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)

                    HStack(spacing: 6) {
                        if user?.subscriptionType == .premium || user?.subscriptionType == .lifetime {
                            Image(systemName: "crown.fill")
                                .font(.system(size: 12))
                                .foregroundColor(SerenityTheme.Colors.warning)

                            Text(user?.subscriptionType.displayName ?? "Premium")
                                .font(SerenityTheme.Typography.caption1)
                                .foregroundColor(SerenityTheme.Colors.warning)
                        } else {
                            Text("free_account".localized())
                                .font(SerenityTheme.Typography.caption1)
                                .foregroundColor(SerenityTheme.Colors.textSecondary)
                        }
                    }

                    Text(user?.email ?? "")
                        .font(SerenityTheme.Typography.caption2)
                        .foregroundColor(SerenityTheme.Colors.textTertiary)
                        .lineLimit(1)
                }

                Spacer()

                // Arrow indicator
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(SerenityTheme.Colors.textTertiary)
            }
            .padding(SerenityTheme.Spacing.md)
            .serenityCardStyle()
        }
    }
}

struct UsageStreaksView: View {
    @StateObject private var usageManager = UsageHistoryManager()
    @State private var currentMonth = Date()
    @Environment(\.dismiss) private var dismiss

    private let calendar = Calendar.current
    private let dateFormatter = DateFormatter.monthYear

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header with stats
                    statsHeader

                    // Calendar view
                    calendarView

                    // Share button
                    shareButton
                }
                .padding()
            }
            .navigationTitle("Usage History")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(SerenityTheme.Colors.primary)
                }
            }
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.blue.opacity(0.8),
                        Color.purple.opacity(0.8)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
            )
        }
    }

    private var statsHeader: some View {
        HStack(spacing: 40) {
            StatCard(
                title: "Total",
                value: "\(usageManager.history.totalDays) days",
                icon: "eyeglasses",
                color: .white
            )

            StatCard(
                title: "Longest",
                value: "\(usageManager.history.longestStreak) days",
                icon: "arrow.right",
                color: .white
            )

            StatCard(
                title: "Current",
                value: "\(usageManager.history.currentStreak) days",
                icon: "clock",
                color: .white
            )
        }
        .foregroundColor(.white)
    }

    private var calendarView: some View {
        VStack(spacing: 16) {
            // Month navigation
            HStack {
                Button(action: previousMonth) {
                    Image(systemName: "chevron.left")
                        .foregroundColor(.white)
                        .font(.title2)
                }

                Spacer()

                Text(dateFormatter.string(from: currentMonth))
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)

                Spacer()

                Button(action: nextMonth) {
                    Image(systemName: "chevron.right")
                        .foregroundColor(.white)
                        .font(.title2)
                }
            }
            .padding(.horizontal)

            // Calendar grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 8) {
                // Weekday headers
                ForEach(["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"], id: \.self) { day in
                    Text(day)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.white.opacity(0.7))
                        .frame(height: 30)
                }

                // Calendar days
                ForEach(calendarDays, id: \.self) { date in
                    CalendarDayView(
                        date: date,
                        hasUsage: usageManager.history.hasUsage(for: date),
                        isCurrentMonth: calendar.isDate(date, equalTo: currentMonth, toGranularity: .month)
                    )
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
        )
    }

    private var shareButton: some View {
        Button(action: shareStreaks) {
            HStack {
                Image(systemName: "square.and.arrow.up")
                Text("Share My Streaks")
                    .fontWeight(.semibold)
            }
            .foregroundColor(.blue)
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.white)
            .cornerRadius(25)
        }
    }

    private var calendarDays: [Date] {
        guard let monthInterval = calendar.dateInterval(of: .month, for: currentMonth) else {
            return []
        }

        let firstOfMonth = monthInterval.start
        let firstWeekday = calendar.component(.weekday, from: firstOfMonth)

        // Adjust for Monday as first day of week
        let daysFromMonday = (firstWeekday + 5) % 7

        guard let startDate = calendar.date(byAdding: .day, value: -daysFromMonday, to: firstOfMonth) else {
            return []
        }

        var days: [Date] = []
        for i in 0..<42 { // 6 weeks * 7 days
            if let date = calendar.date(byAdding: .day, value: i, to: startDate) {
                days.append(date)
            }
        }

        return days
    }

    private func previousMonth() {
        if let newMonth = calendar.date(byAdding: .month, value: -1, to: currentMonth) {
            currentMonth = newMonth
        }
    }

    private func nextMonth() {
        if let newMonth = calendar.date(byAdding: .month, value: 1, to: currentMonth) {
            currentMonth = newMonth
        }
    }

    private func shareStreaks() {
        // Implement sharing functionality
        let text = "I've been using the app for \(usageManager.history.totalDays) days with a current streak of \(usageManager.history.currentStreak) days!"

        let activityVC = UIActivityViewController(activityItems: [text], applicationActivities: nil)

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityVC, animated: true)
        }
    }
}

struct CalendarDayView: View {
    let date: Date
    let hasUsage: Bool
    let isCurrentMonth: Bool

    private var dayNumber: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"
        return formatter.string(from: date)
    }

    private var isToday: Bool {
        Calendar.current.isDateInToday(date)
    }

    var body: some View {
        ZStack {
            Circle()
                .fill(backgroundColor)
                .frame(width: 36, height: 36)

            Text(dayNumber)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(textColor)
        }
        .opacity(isCurrentMonth ? 1.0 : 0.3)
    }

    private var backgroundColor: Color {
        if hasUsage {
            return .white
        } else if isToday {
            return .white.opacity(0.3)
        } else {
            return .clear
        }
    }

    private var textColor: Color {
        if hasUsage {
            return .blue
        } else {
            return .white
        }
    }
}

#Preview {
    MoreView()
}
