//
//  ContactUsView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct ContactUsView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var selectedCategory = ContactCategory.general
    @State private var subject = ""
    @State private var message = ""
    @State private var email = ""
    @State private var showingSuccessAlert = false
    @State private var isSubmitting = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: SerenityTheme.Spacing.xl) {
                    // Header
                    headerSection
                    
                    // Contact Form
                    contactFormSection
                    
                    // Quick Contact Options
                    quickContactSection
                }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
                .padding(.vertical, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationTitle("contact_us".localized())
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("cancel".localized()) {
                        dismiss()
                    }
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
                }
            }
        }
        .alert("message_sent".localized(), isPresented: $showingSuccessAlert) {
            Button("ok".localized()) {
                dismiss()
            }
        } message: {
            Text("message_sent_confirmation".localized())
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: SerenityTheme.Spacing.md) {
            Image(systemName: "envelope.circle.fill")
                .font(.system(size: 50))
                .foregroundColor(SerenityTheme.Colors.primary)
            
            Text("get_in_touch".localized())
                .font(SerenityTheme.Typography.title2)
                .fontWeight(.bold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            Text("wed_love_hear_from_you".localized())
                .font(SerenityTheme.Typography.body)
                .foregroundColor(SerenityTheme.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
    }
    
    private var contactFormSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.lg) {
            Text("send_us_message".localized())
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            VStack(spacing: SerenityTheme.Spacing.md) {
                // Category Selection
                VStack(alignment: .leading, spacing: SerenityTheme.Spacing.sm) {
                    Text("category".localized())
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.medium)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                    
                    Menu {
                        ForEach(ContactCategory.allCases, id: \.self) { category in
                            Button(category.displayName) {
                                selectedCategory = category
                            }
                        }
                    } label: {
                        HStack {
                            Text(selectedCategory.displayName)
                                .font(SerenityTheme.Typography.body)
                                .foregroundColor(SerenityTheme.Colors.textPrimary)
                            
                            Spacer()
                            
                            Image(systemName: "chevron.down")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(SerenityTheme.Colors.textTertiary)
                        }
                        .padding(SerenityTheme.Spacing.md)
                        .background(SerenityTheme.Colors.cardBackground)
                        .cornerRadius(SerenityTheme.CornerRadius.small)
                    }
                }
                
                // Email Field
                VStack(alignment: .leading, spacing: SerenityTheme.Spacing.sm) {
                    Text("email".localized())
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.medium)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                    
                    TextField("<EMAIL>", text: $email)
                        .keyboardType(.emailAddress)
                        .autocapitalization(.none)
                        .serenityTextFieldStyle()
                }
                
                // Subject Field
                VStack(alignment: .leading, spacing: SerenityTheme.Spacing.sm) {
                    Text("subject".localized())
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.medium)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                    
                    TextField("Brief description of your inquiry", text: $subject)
                        .serenityTextFieldStyle()
                }
                
                // Message Field
                VStack(alignment: .leading, spacing: SerenityTheme.Spacing.sm) {
                    Text("message".localized())
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.medium)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                    
                    TextEditor(text: $message)
                        .font(SerenityTheme.Typography.body)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                        .frame(minHeight: 120)
                        .padding(SerenityTheme.Spacing.md)
                        .background(SerenityTheme.Colors.cardBackground)
                        .cornerRadius(SerenityTheme.CornerRadius.small)
                        .overlay(
                            RoundedRectangle(cornerRadius: SerenityTheme.CornerRadius.small)
                                .stroke(SerenityTheme.Colors.textTertiary.opacity(0.3), lineWidth: 1)
                        )
                }
                
                // Submit Button
                Button(action: submitMessage) {
                    HStack {
                        if isSubmitting {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                        } else {
                            Text("send_message".localized())
                                .font(SerenityTheme.Typography.headline)
                                .fontWeight(.semibold)
                        }
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, SerenityTheme.Spacing.md + 4)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                SerenityTheme.Colors.primary,
                                SerenityTheme.Colors.primaryDark
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(SerenityTheme.CornerRadius.medium)
                }
                .disabled(isSubmitting || email.isEmpty || subject.isEmpty || message.isEmpty)
                .opacity((isSubmitting || email.isEmpty || subject.isEmpty || message.isEmpty) ? 0.6 : 1.0)
            }
        }
        .padding(SerenityTheme.Spacing.lg)
        .serenityCardStyle()
    }
    
    private var quickContactSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("other_ways_to_reach_us".localized())
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            VStack(spacing: SerenityTheme.Spacing.sm) {
                QuickContactRow(
                    icon: "envelope.fill",
                    title: "Email Support",
                    subtitle: "<EMAIL>",
                    action: {
                        if let url = URL(string: "mailto:<EMAIL>") {
                            UIApplication.shared.open(url)
                        }
                    }
                )
                
                QuickContactRow(
                    icon: "message.fill",
                    title: "Live Chat",
                    subtitle: "Available 9 AM - 6 PM EST",
                    action: {
                        // Open live chat
                    }
                )
                
                QuickContactRow(
                    icon: "globe",
                    title: "Website",
                    subtitle: "www.serenityapp.com",
                    action: {
                        if let url = URL(string: "https://www.serenityapp.com") {
                            UIApplication.shared.open(url)
                        }
                    }
                )
            }
        }
    }
    
    private func submitMessage() {
        isSubmitting = true
        
        // Simulate API call
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            isSubmitting = false
            showingSuccessAlert = true
        }
    }
}

enum ContactCategory: String, CaseIterable {
    case general = "general"
    case technical = "technical"
    case billing = "billing"
    case feature = "feature"
    case bug = "bug"
    
    var displayName: String {
        switch self {
        case .general:
            return "General Inquiry"
        case .technical:
            return "Technical Support"
        case .billing:
            return "Billing & Subscription"
        case .feature:
            return "Feature Request"
        case .bug:
            return "Bug Report"
        }
    }
}

struct QuickContactRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: SerenityTheme.Spacing.md) {
                Image(systemName: icon)
                    .font(.system(size: 16))
                    .foregroundColor(SerenityTheme.Colors.primary)
                    .frame(width: 24, height: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(SerenityTheme.Typography.callout)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                    
                    Text(subtitle)
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(SerenityTheme.Colors.textTertiary)
            }
            .padding(SerenityTheme.Spacing.md)
            .serenityCardStyle()
        }
    }
}

#Preview {
    ContactUsView()
}
