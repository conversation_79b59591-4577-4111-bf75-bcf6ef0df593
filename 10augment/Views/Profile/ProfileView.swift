//
//  ProfileView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct ProfileView: View {
    @StateObject private var authService = AuthenticationService.shared
    @Environment(\.dismiss) private var dismiss
    @State private var showingImagePicker = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                    VStack(spacing: SerenityTheme.Spacing.xl) {
                        // Profile Header
                        profileHeaderSection
                    }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
                .padding(.vertical, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationTitle("profile".localized())
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("done".localized()) {
                        dismiss()
                    }
                    .foregroundColor(SerenityTheme.Colors.primary)
                }
            }
        }
    }
    
    private var profileHeaderSection: some View {
        VStack(spacing: SerenityTheme.Spacing.lg) {
            // Profile Image
            Button(action: { showingImagePicker = true }) {
                AsyncImage(url: URL(string: authService.currentUser?.profileImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Circle()
                        .fill(SerenityTheme.Colors.primary)
                        .overlay(
                            Text(authService.currentUser?.name.prefix(1).uppercased() ?? "U")
                                .font(SerenityTheme.Typography.title1)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        )
                }
                .frame(width: 100, height: 100)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(SerenityTheme.Colors.primary, lineWidth: 3)
                )
            }
            
            VStack(spacing: 4) {
                Text(authService.currentUser?.name ?? "user".localized())
                    .font(SerenityTheme.Typography.title2)
                    .fontWeight(.bold)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)
                
                Text(authService.currentUser?.email ?? "<EMAIL>")
                    .font(SerenityTheme.Typography.callout)
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
                
                HStack {
                    Image(systemName: "crown.fill")
                        .font(.system(size: 12))
                        .foregroundColor(SerenityTheme.Colors.warning)
                    
                    Text(authService.currentUser?.subscriptionType.displayName ?? "free".localized())
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.textTertiary)
                }
            }
        }
    }
    

    

    

}

#Preview {
    ProfileView()
}
