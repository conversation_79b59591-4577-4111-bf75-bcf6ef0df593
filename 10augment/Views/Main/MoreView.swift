//
//  MoreView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct MoreView: View {
    @StateObject private var authService = AuthenticationService()
    @State private var showingSettings = false
    @State private var showingProfile = false
    @State private var showingAuth = false
    @State private var showingNotifications = false
    @State private var showingHelpCenter = false
    @State private var showingContactUs = false
    @State private var showingTerms = false
    @State private var showingPrivacy = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                    VStack(spacing: SerenityTheme.Spacing.xl) {
                        // Header
                        headerSection
                        
                        // Account Section
                        accountSection
                        
                        // App Settings
                        appSettingsSection
                        
                        // Support Section
                        supportSection
                        
                        // Sign Out
                        signOutSection
                    }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingSettings) {
            SettingsView()
        }
        .sheet(isPresented: $showingProfile) {
            ProfileView()
        }
        .sheet(isPresented: $showingAuth) {
            AuthenticationView()
        }
        .sheet(isPresented: $showingNotifications) {
            NotificationSettingsView()
        }
        .sheet(isPresented: $showingHelpCenter) {
            HelpCenterView()
        }
        .sheet(isPresented: $showingContactUs) {
            ContactUsView()
        }
        .sheet(isPresented: $showingTerms) {
            TermsOfServiceView()
        }
        .sheet(isPresented: $showingPrivacy) {
            PrivacyPolicyView()
        }
    }
    
    private var headerSection: some View {
        HStack {
            Text("Settings")
                .font(SerenityTheme.Typography.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            Spacer()
        }
        .padding(.top, SerenityTheme.Spacing.lg)
    }
    
    private var accountSection: some View {
        VStack(spacing: SerenityTheme.Spacing.sm) {
            if authService.isAuthenticated {
                // Logged in - Show user info
                UserInfoCard(user: authService.currentUser) {
                    showingProfile = true
                }

                SettingsRow(
                    icon: "crown",
                    title: "Premium",
                    subtitle: authService.currentUser?.subscriptionType.displayName ?? "Free",
                    action: {}
                )
            } else {
                // Not logged in - Show login option
                SettingsRow(
                    icon: "person.circle",
                    title: "Account",
                    subtitle: "Sign in or create account",
                    action: { showingAuth = true }
                )
            }
        }
    }
    
    private var appSettingsSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("App Settings")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            VStack(spacing: SerenityTheme.Spacing.sm) {
                SettingsRow(
                    icon: "bell",
                    title: "Notifications",
                    subtitle: "Reminders and updates",
                    action: { showingNotifications = true }
                )

                SettingsRow(
                    icon: "paintbrush",
                    title: "theme".localized(),
                    subtitle: "App appearance",
                    action: { showingSettings = true }
                )
            }
        }
    }
    
    private var supportSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Support")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            VStack(spacing: SerenityTheme.Spacing.sm) {
                SettingsRow(
                    icon: "questionmark.circle",
                    title: "Help Center",
                    subtitle: "Get help and support",
                    action: { showingHelpCenter = true }
                )

                SettingsRow(
                    icon: "envelope",
                    title: "Contact Us",
                    subtitle: "Send us feedback",
                    action: { showingContactUs = true }
                )

                SettingsRow(
                    icon: "doc.text",
                    title: "Terms of Service",
                    subtitle: "Legal information",
                    action: { showingTerms = true }
                )

                SettingsRow(
                    icon: "hand.raised",
                    title: "Privacy Policy",
                    subtitle: "How we protect your data",
                    action: { showingPrivacy = true }
                )
            }
        }
    }
    
    private var signOutSection: some View {
        VStack(spacing: SerenityTheme.Spacing.md) {
            Button(action: {
                authService.signOut()
            }) {
                HStack {
                    Image(systemName: "rectangle.portrait.and.arrow.right")
                        .font(.system(size: 16))
                    
                    Text("Sign Out")
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.medium)
                }
                .foregroundColor(SerenityTheme.Colors.error)
                .frame(maxWidth: .infinity)
                .padding(.vertical, SerenityTheme.Spacing.md)
                .background(SerenityTheme.Colors.cardBackground)
                .cornerRadius(SerenityTheme.CornerRadius.small)
            }
            
            Text("Version 1.0.0")
                .font(SerenityTheme.Typography.caption2)
                .foregroundColor(SerenityTheme.Colors.textTertiary)
        }
    }
}

struct SettingsRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: SerenityTheme.Spacing.md) {
                Image(systemName: icon)
                    .font(.system(size: 16))
                    .foregroundColor(SerenityTheme.Colors.primary)
                    .frame(width: 24, height: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(SerenityTheme.Typography.callout)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                    
                    Text(subtitle)
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(SerenityTheme.Colors.textTertiary)
            }
            .padding(SerenityTheme.Spacing.md)
            .serenityCardStyle()
        }
    }
}

struct UserInfoCard: View {
    let user: User?
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: SerenityTheme.Spacing.md) {
                // User Avatar
                AsyncImage(url: URL(string: user?.profileImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Circle()
                        .fill(SerenityTheme.Colors.primary)
                        .overlay(
                            Text(user?.name.prefix(1).uppercased() ?? "U")
                                .font(SerenityTheme.Typography.headline)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        )
                }
                .frame(width: 50, height: 50)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(SerenityTheme.Colors.primary, lineWidth: 2)
                )

                // User Info
                VStack(alignment: .leading, spacing: 4) {
                    Text(user?.name ?? "User")
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.semibold)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)

                    HStack(spacing: 6) {
                        if user?.subscriptionType == .premium || user?.subscriptionType == .lifetime {
                            Image(systemName: "crown.fill")
                                .font(.system(size: 12))
                                .foregroundColor(SerenityTheme.Colors.warning)

                            Text(user?.subscriptionType.displayName ?? "Premium")
                                .font(SerenityTheme.Typography.caption1)
                                .foregroundColor(SerenityTheme.Colors.warning)
                        } else {
                            Text("Free Account")
                                .font(SerenityTheme.Typography.caption1)
                                .foregroundColor(SerenityTheme.Colors.textSecondary)
                        }
                    }

                    Text(user?.email ?? "")
                        .font(SerenityTheme.Typography.caption2)
                        .foregroundColor(SerenityTheme.Colors.textTertiary)
                        .lineLimit(1)
                }

                Spacer()

                // Arrow indicator
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(SerenityTheme.Colors.textTertiary)
            }
            .padding(SerenityTheme.Spacing.md)
            .serenityCardStyle()
        }
    }
}

#Preview {
    MoreView()
}
