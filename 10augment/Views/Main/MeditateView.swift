//
//  MeditateView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct MeditateView: View {
    @State private var selectedTechnique: BreathingTechnique?

    private var meditateTechniques: [BreathingTechnique] {
        BreathingTechnique.allTechniques.filter { $0.category == .meditate }
    }

    private var stressReductionTechniques: [BreathingTechnique] {
        BreathingTechnique.allTechniques.filter { $0.category == .stressReduction }
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: SerenityTheme.Spacing.xl) {
                    // Header
                    headerSection

                    // Meditate Techniques
                    meditateTechniquesSection

                    // Stress Reduction Techniques
                    stressReductionTechniquesSection

                    // Meditation Tips
                    meditationTipsSection
                }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationBarHidden(true)
        }
        .sheet(item: $selectedTechnique) { technique in
            BreathingExerciseView(technique: technique)
        }
    }

    private var headerSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("meditate".localized())
                .font(SerenityTheme.Typography.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            Text("meditation_subtitle".localized())
                .font(SerenityTheme.Typography.body)
                .foregroundColor(SerenityTheme.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(.top, SerenityTheme.Spacing.lg)
    }

    private var meditateTechniquesSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("meditate_techniques".localized())
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            VStack(spacing: SerenityTheme.Spacing.sm) {
                ForEach(meditateTechniques) { technique in
                    BreathingTechniqueCard(technique: technique) {
                        selectedTechnique = technique
                    }
                }
            }
        }
    }

    private var stressReductionTechniquesSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("stress_reduction".localized())
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            VStack(spacing: SerenityTheme.Spacing.sm) {
                ForEach(stressReductionTechniques) { technique in
                    BreathingTechniqueCard(technique: technique) {
                        selectedTechnique = technique
                    }
                }
            }
        }
    }

    private var meditationTipsSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("meditation_tips".localized())
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            VStack(spacing: SerenityTheme.Spacing.sm) {
                MeditationTipCard(
                    icon: "figure.seated.side",
                    title: "find_your_posture".localized(),
                    description: "posture_desc".localized()
                )

                MeditationTipCard(
                    icon: "eye.slash",
                    title: "focus_your_gaze".localized(),
                    description: "gaze_desc".localized()
                )

                MeditationTipCard(
                    icon: "brain.head.profile",
                    title: "observe_thoughts".localized(),
                    description: "thoughts_desc".localized()
                )

                MeditationTipCard(
                    icon: "clock",
                    title: "start_small".localized(),
                    description: "start_small_desc".localized()
                )

                MeditationTipCard(
                    icon: "location",
                    title: "create_sacred_space".localized(),
                    description: "sacred_space_desc".localized()
                )

                MeditationTipCard(
                    icon: "heart",
                    title: "practice_self_compassion".localized(),
                    description: "self_compassion_desc".localized()
                )
            }
        }
    }
}

struct MeditationTipCard: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: SerenityTheme.Spacing.md) {
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(SerenityTheme.Colors.meditate)
                .frame(width: 40, height: 40)
                .background(SerenityTheme.Colors.meditate.opacity(0.2))
                .clipShape(Circle())

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(SerenityTheme.Typography.callout)
                    .fontWeight(.medium)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)

                Text(description)
                    .font(SerenityTheme.Typography.caption1)
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
            }

            Spacer()
        }
        .padding(SerenityTheme.Spacing.md)
        .serenityCardStyle()
    }
}

#Preview {
    MeditateView()
}
