//
//  HomeView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct HomeView: View {
    @StateObject private var authService = AuthenticationService()
    @EnvironmentObject private var localizationManager: LocalizationManager
    @State private var showingBreathingExercise = false
    @State private var selectedTechnique: BreathingTechnique?
    
    var body: some View {
        NavigationView {
            ScrollView {
                    VStack(spacing: SerenityTheme.Spacing.xl) {
                        // Header
                        headerSection
                        
                        // Quick Start Section
                        quickStartSection
                        
                        // Today's Recommendation
                        recommendationSection
                        
                        // Your Stats
                        statsSection

                        // Achievements
                        achievementsSection

                        // Recent Activity
                        recentActivitySection
                    }
                    .padding(.horizontal, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationBarHidden(true)
        }
        .sheet(item: $selectedTechnique) { technique in
            BreathingExerciseView(technique: technique)
        }
    }
    
    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack(spacing: 8) {
                    Text(currentGreeting)
                        .font(.system(size: 24, weight: .bold, design: .rounded))
                        .foregroundColor(greetingColor)

                    Text(greetingIcon)
                        .font(.system(size: 24))
                }

                Text(authService.currentUser?.name ?? "welcome".localized())
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
            }
            
            Spacer()
            
            // Profile Button
            Button(action: {}) {
                AsyncImage(url: URL(string: authService.currentUser?.profileImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Circle()
                        .fill(SerenityTheme.Colors.primary)
                        .overlay(
                            Text(authService.currentUser?.name.prefix(1).uppercased() ?? "U")
                                .font(SerenityTheme.Typography.headline)
                                .foregroundColor(.white)
                        )
                }
                .frame(width: 40, height: 40)
                .clipShape(Circle())
            }
        }
        .padding(.top, SerenityTheme.Spacing.lg)
    }
    
    private var quickStartSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("quick_start".localized())
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: SerenityTheme.Spacing.md) {
                    ForEach(BreathingTechnique.allTechniques.prefix(3)) { technique in
                        QuickStartCard(technique: technique) {
                            selectedTechnique = technique
                        }
                    }
                }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
            }
            .padding(.horizontal, -SerenityTheme.Spacing.lg)
        }
    }
    
    private var recommendationSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("todays_recommendation".localized())
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            RecommendationCard(technique: BreathingTechnique.breathing478) {
                selectedTechnique = BreathingTechnique.breathing478
            }
        }
    }
    
    private var statsSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("your_stats".localized())
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            HStack(spacing: SerenityTheme.Spacing.md) {
                StatCard(
                    title: "Total Sessions",
                    value: "\(authService.currentUser?.stats.totalSessions ?? 0)",
                    icon: "checkmark.circle.fill",
                    color: SerenityTheme.Colors.success
                )

                StatCard(
                    title: "Total Minutes",
                    value: "\(authService.currentUser?.stats.totalMinutes ?? 0)",
                    icon: "clock.fill",
                    color: SerenityTheme.Colors.primary
                )
            }

            HStack(spacing: SerenityTheme.Spacing.md) {
                StatCard(
                    title: "Current Streak",
                    value: "\(authService.currentUser?.stats.currentStreak ?? 0) days",
                    icon: "flame.fill",
                    color: SerenityTheme.Colors.warning
                )

                StatCard(
                    title: "Longest Streak",
                    value: "\(authService.currentUser?.stats.longestStreak ?? 0) days",
                    icon: "trophy.fill",
                    color: SerenityTheme.Colors.accent
                )
            }
        }
    }
    
    private var achievementsSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Achievements")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: SerenityTheme.Spacing.md) {
                    AchievementBadge(
                        title: "First Session",
                        description: "Complete your first breathing session",
                        icon: "star.fill",
                        isUnlocked: true
                    )

                    AchievementBadge(
                        title: "Week Warrior",
                        description: "Practice for 7 days in a row",
                        icon: "calendar",
                        isUnlocked: false
                    )

                    AchievementBadge(
                        title: "Zen Master",
                        description: "Complete 100 sessions",
                        icon: "brain.head.profile",
                        isUnlocked: false
                    )
                }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
            }
            .padding(.horizontal, -SerenityTheme.Spacing.lg)
        }
    }

    private var recentActivitySection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Recent Activity")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            VStack(spacing: SerenityTheme.Spacing.sm) {
                ActivityRow(
                    technique: "4-7-8 Breathing",
                    duration: "5:30",
                    date: "Today",
                    completion: 100
                )

                ActivityRow(
                    technique: "Box Breathing",
                    duration: "8:00",
                    date: "Yesterday",
                    completion: 85
                )

                ActivityRow(
                    technique: "Equal Breathing",
                    duration: "3:45",
                    date: "2 days ago",
                    completion: 100
                )
            }
        }
    }
    
    private var timeOfDay: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12:
            return "morning".localized()
        case 12..<17:
            return "afternoon".localized()
        case 17..<21:
            return "evening".localized()
        default:
            return "night".localized()
        }
    }

    private var currentGreeting: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12:
            return "good_morning".localized()
        case 12..<17:
            return "good_afternoon".localized()
        case 17..<21:
            return "good_evening".localized()
        default:
            return "good_night".localized()
        }
    }

    private var greetingColor: Color {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12:
            return Color.green // Good Morning - Green
        case 12..<17:
            return Color.orange // Good Afternoon - Orange
        case 17..<21:
            return Color.blue // Good Evening - Blue
        default:
            return Color.purple // Good Night - Purple
        }
    }

    private var greetingIcon: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12:
            return "🌅" // Good Morning - Sunrise
        case 12..<17:
            return "☀️" // Good Afternoon - Sun
        case 17..<21:
            return "🌇" // Good Evening - Sunset
        default:
            return "🌙" // Good Night - Moon
        }
    }
}

struct QuickStartCard: View {
    let technique: BreathingTechnique
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: SerenityTheme.Spacing.sm) {
                // Technique Image
                RoundedRectangle(cornerRadius: SerenityTheme.CornerRadius.small)
                    .fill(technique.category.color.opacity(0.3))
                    .frame(height: 80)
                    .overlay(
                        Image(systemName: "wind")
                            .font(.system(size: 24))
                            .foregroundColor(technique.category.color)
                    )

                VStack(spacing: 4) {
                    Text(technique.name)
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.medium)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                        .lineLimit(2)
                        .multilineTextAlignment(.center)

                    Text("\(Int(technique.duration / 60)) min")
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                }
            }
            .frame(width: 160, height: 140)
            .padding(SerenityTheme.Spacing.md)
            .serenityCardStyle()
        }
    }
}

struct RecommendationCard: View {
    let technique: BreathingTechnique
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: SerenityTheme.Spacing.md) {
                // Technique Image
                RoundedRectangle(cornerRadius: SerenityTheme.CornerRadius.small)
                    .fill(technique.category.color.opacity(0.3))
                    .frame(width: 80, height: 80)
                    .overlay(
                        Image(systemName: "moon.stars")
                            .font(.system(size: 24))
                            .foregroundColor(technique.category.color)
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(technique.name)
                        .font(SerenityTheme.Typography.headline)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                    
                    Text(technique.description)
                        .font(SerenityTheme.Typography.callout)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                        .lineLimit(2)
                    
                    Text("\(Int(technique.duration / 60)) minutes")
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.primary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(SerenityTheme.Colors.textTertiary)
            }
            .padding(SerenityTheme.Spacing.md)
            .serenityCardStyle()
        }
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: SerenityTheme.Spacing.sm) {
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(color)

            Text(value)
                .font(SerenityTheme.Typography.headline)
                .fontWeight(.bold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            Text(title)
                .font(SerenityTheme.Typography.caption1)
                .foregroundColor(SerenityTheme.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(SerenityTheme.Spacing.md)
        .serenityCardStyle()
    }
}

struct AchievementBadge: View {
    let title: String
    let description: String
    let icon: String
    let isUnlocked: Bool

    var body: some View {
        VStack(spacing: SerenityTheme.Spacing.sm) {
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(isUnlocked ? SerenityTheme.Colors.warning : SerenityTheme.Colors.textTertiary)

            Text(title)
                .font(SerenityTheme.Typography.callout)
                .fontWeight(.medium)
                .foregroundColor(isUnlocked ? SerenityTheme.Colors.textPrimary : SerenityTheme.Colors.textTertiary)
                .multilineTextAlignment(.center)

            Text(description)
                .font(SerenityTheme.Typography.caption2)
                .foregroundColor(SerenityTheme.Colors.textTertiary)
                .multilineTextAlignment(.center)
        }
        .frame(width: 120)
        .padding(SerenityTheme.Spacing.md)
        .background(
            isUnlocked ? SerenityTheme.Colors.cardBackground : SerenityTheme.Colors.cardBackground.opacity(0.5)
        )
        .cornerRadius(SerenityTheme.CornerRadius.medium)
        .overlay(
            RoundedRectangle(cornerRadius: SerenityTheme.CornerRadius.medium)
                .stroke(
                    isUnlocked ? SerenityTheme.Colors.warning.opacity(0.3) : SerenityTheme.Colors.textTertiary.opacity(0.2),
                    lineWidth: 1
                )
        )
    }
}

struct ActivityRow: View {
    let technique: String
    let duration: String
    let date: String
    let completion: Int

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(technique)
                    .font(SerenityTheme.Typography.callout)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)

                Text("\(duration) • \(date)")
                    .font(SerenityTheme.Typography.caption1)
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
            }

            Spacer()

            Text("\(completion)%")
                .font(SerenityTheme.Typography.caption1)
                .foregroundColor(completion == 100 ? SerenityTheme.Colors.success : SerenityTheme.Colors.textSecondary)
        }
        .padding(SerenityTheme.Spacing.md)
        .serenityCardStyle()
    }
}

#Preview {
    HomeView()
}
