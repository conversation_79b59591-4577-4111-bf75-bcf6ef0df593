//
//  ProfileView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct ProfileView: View {
    @StateObject private var authService = AuthenticationService.shared
    @StateObject private var usageManager = UsageHistoryManager()
    @Environment(\.dismiss) private var dismiss
    @State private var showingImagePicker = false
    @State private var currentMonth = Date()

    private let calendar = Calendar.current
    private let dateFormatter = DateFormatter.monthYear

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: SerenityTheme.Spacing.xl) {
                    // Profile Header
                    profileHeaderSection

                    // Usage History Section
                    usageHistorySection

                    // Sign Out Section
                    signOutSection

                    // Version Information
                    versionSection
                }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
                .padding(.vertical, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationTitle("profile".localized())
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("done".localized()) {
                        dismiss()
                    }
                    .foregroundColor(SerenityTheme.Colors.primary)
                }
            }
        }
    }
    
    private var profileHeaderSection: some View {
        VStack(spacing: SerenityTheme.Spacing.lg) {
            // Profile Image
            Button(action: { showingImagePicker = true }) {
                AsyncImage(url: URL(string: authService.currentUser?.profileImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Circle()
                        .fill(SerenityTheme.Colors.primary)
                        .overlay(
                            Text(authService.currentUser?.name.prefix(1).uppercased() ?? "U")
                                .font(SerenityTheme.Typography.title1)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        )
                }
                .frame(width: 100, height: 100)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(SerenityTheme.Colors.primary, lineWidth: 3)
                )
            }
            
            VStack(spacing: 4) {
                Text(authService.currentUser?.name ?? "user".localized())
                    .font(SerenityTheme.Typography.title2)
                    .fontWeight(.bold)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)
                
                Text(authService.currentUser?.email ?? "<EMAIL>")
                    .font(SerenityTheme.Typography.callout)
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
                
                HStack {
                    Image(systemName: "crown.fill")
                        .font(.system(size: 12))
                        .foregroundColor(SerenityTheme.Colors.warning)
                    
                    Text(authService.currentUser?.subscriptionType.displayName ?? "free".localized())
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.textTertiary)
                }
            }
        }
    }

    private var usageHistorySection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.lg) {
            // Section Header
            HStack {
                Text("Usage History")
                    .font(SerenityTheme.Typography.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)

                Spacer()
            }

            // Stats Cards
            HStack(spacing: SerenityTheme.Spacing.md) {
                StatCard(
                    title: "Total",
                    value: "\(usageManager.history.totalDays)",
                    icon: "eyeglasses",
                    color: SerenityTheme.Colors.primary
                )

                StatCard(
                    title: "Current",
                    value: "\(usageManager.history.currentStreak)",
                    icon: "clock",
                    color: SerenityTheme.Colors.success
                )

                StatCard(
                    title: "Longest",
                    value: "\(usageManager.history.longestStreak)",
                    icon: "arrow.right",
                    color: SerenityTheme.Colors.warning
                )
            }

            // Calendar View
            calendarView
        }
        .padding(SerenityTheme.Spacing.lg)
        .serenityCardStyle()
    }

    private var calendarView: some View {
        VStack(spacing: SerenityTheme.Spacing.md) {
            // Month navigation
            HStack {
                Button(action: previousMonth) {
                    Image(systemName: "chevron.left")
                        .foregroundColor(SerenityTheme.Colors.primary)
                        .font(.title3)
                }

                Spacer()

                Text(dateFormatter.string(from: currentMonth))
                    .font(SerenityTheme.Typography.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)

                Spacer()

                Button(action: nextMonth) {
                    Image(systemName: "chevron.right")
                        .foregroundColor(SerenityTheme.Colors.primary)
                        .font(.title3)
                }
            }

            // Calendar grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 8) {
                // Weekday headers
                ForEach(["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"], id: \.self) { day in
                    Text(day)
                        .font(SerenityTheme.Typography.caption2)
                        .fontWeight(.medium)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                        .frame(height: 30)
                }

                // Calendar days
                ForEach(calendarDays, id: \.self) { date in
                    CalendarDayView(
                        date: date,
                        hasUsage: usageManager.history.hasUsage(for: date),
                        isCurrentMonth: calendar.isDate(date, equalTo: currentMonth, toGranularity: .month)
                    )
                }
            }
        }
    }

    private var calendarDays: [Date] {
        guard let monthInterval = calendar.dateInterval(of: .month, for: currentMonth) else {
            return []
        }

        let firstOfMonth = monthInterval.start
        let firstWeekday = calendar.component(.weekday, from: firstOfMonth)

        // Adjust for Monday as first day of week
        let daysFromMonday = (firstWeekday + 5) % 7

        guard let startDate = calendar.date(byAdding: .day, value: -daysFromMonday, to: firstOfMonth) else {
            return []
        }

        var days: [Date] = []
        for i in 0..<42 { // 6 weeks * 7 days
            if let date = calendar.date(byAdding: .day, value: i, to: startDate) {
                days.append(date)
            }
        }

        return days
    }

    private func previousMonth() {
        if let newMonth = calendar.date(byAdding: .month, value: -1, to: currentMonth) {
            currentMonth = newMonth
        }
    }

    private func nextMonth() {
        if let newMonth = calendar.date(byAdding: .month, value: 1, to: currentMonth) {
            currentMonth = newMonth
        }
    }

    private var signOutSection: some View {
        Button(action: {
            authService.signOut()
            dismiss() // Close the profile view after signing out
        }) {
            HStack {
                Image(systemName: "rectangle.portrait.and.arrow.right")
                    .font(.system(size: 16))

                Text("Sign Out")
                    .font(SerenityTheme.Typography.callout)
                    .fontWeight(.medium)
            }
            .foregroundColor(SerenityTheme.Colors.error)
            .frame(maxWidth: .infinity)
            .padding(.vertical, SerenityTheme.Spacing.md)
            .background(SerenityTheme.Colors.cardBackground)
            .cornerRadius(SerenityTheme.CornerRadius.small)
        }
        .padding(SerenityTheme.Spacing.lg)
        .serenityCardStyle()
    }

    private var versionSection: some View {
        Text("Version 1.0.0")
            .font(SerenityTheme.Typography.caption2)
            .foregroundColor(SerenityTheme.Colors.textTertiary)
            .frame(maxWidth: .infinity)
            .padding(.vertical, SerenityTheme.Spacing.sm)
    }
}

struct CalendarDayView: View {
    let date: Date
    let hasUsage: Bool
    let isCurrentMonth: Bool

    private var dayNumber: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"
        return formatter.string(from: date)
    }

    private var isToday: Bool {
        Calendar.current.isDateInToday(date)
    }

    var body: some View {
        ZStack {
            Circle()
                .fill(backgroundColor)
                .frame(width: 32, height: 32)

            Text(dayNumber)
                .font(SerenityTheme.Typography.caption1)
                .fontWeight(.medium)
                .foregroundColor(textColor)
        }
        .opacity(isCurrentMonth ? 1.0 : 0.3)
    }

    private var backgroundColor: Color {
        if hasUsage {
            return SerenityTheme.Colors.primary
        } else if isToday {
            return SerenityTheme.Colors.primary.opacity(0.2)
        } else {
            return Color.clear
        }
    }

    private var textColor: Color {
        if hasUsage {
            return .white
        } else if isToday {
            return SerenityTheme.Colors.primary
        } else {
            return SerenityTheme.Colors.textPrimary
        }
    }
}

#Preview {
    ProfileView()
}
