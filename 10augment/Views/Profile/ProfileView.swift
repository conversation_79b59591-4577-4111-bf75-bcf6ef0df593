//
//  ProfileView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct ProfileView: View {
    @StateObject private var authService = AuthenticationService()
    @Environment(\.dismiss) private var dismiss
    @State private var showingImagePicker = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                    VStack(spacing: SerenityTheme.Spacing.xl) {
                        // Profile Header
                        profileHeaderSection
                        
                        // Stats Section
                        statsSection
                        
                        // Achievements Section
                        achievementsSection
                        
                        // Recent Activity
                        recentActivitySection
                    }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
                .padding(.vertical, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationTitle("Profile")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(SerenityTheme.Colors.primary)
                }
            }
        }
    }
    
    private var profileHeaderSection: some View {
        VStack(spacing: SerenityTheme.Spacing.lg) {
            // Profile Image
            Button(action: { showingImagePicker = true }) {
                AsyncImage(url: URL(string: authService.currentUser?.profileImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Circle()
                        .fill(SerenityTheme.Colors.primary)
                        .overlay(
                            Text(authService.currentUser?.name.prefix(1).uppercased() ?? "U")
                                .font(SerenityTheme.Typography.title1)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        )
                }
                .frame(width: 100, height: 100)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(SerenityTheme.Colors.primary, lineWidth: 3)
                )
            }
            
            VStack(spacing: 4) {
                Text(authService.currentUser?.name ?? "User")
                    .font(SerenityTheme.Typography.title2)
                    .fontWeight(.bold)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)
                
                Text(authService.currentUser?.email ?? "<EMAIL>")
                    .font(SerenityTheme.Typography.callout)
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
                
                HStack {
                    Image(systemName: "crown.fill")
                        .font(.system(size: 12))
                        .foregroundColor(SerenityTheme.Colors.warning)
                    
                    Text(authService.currentUser?.subscriptionType.displayName ?? "Free")
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.textTertiary)
                }
            }
        }
    }
    
    private var statsSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Your Stats")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            HStack(spacing: SerenityTheme.Spacing.md) {
                StatCard(
                    title: "Total Sessions",
                    value: "\(authService.currentUser?.stats.totalSessions ?? 0)",
                    icon: "checkmark.circle.fill",
                    color: SerenityTheme.Colors.success
                )
                
                StatCard(
                    title: "Total Minutes",
                    value: "\(authService.currentUser?.stats.totalMinutes ?? 0)",
                    icon: "clock.fill",
                    color: SerenityTheme.Colors.primary
                )
            }
            
            HStack(spacing: SerenityTheme.Spacing.md) {
                StatCard(
                    title: "Current Streak",
                    value: "\(authService.currentUser?.stats.currentStreak ?? 0) days",
                    icon: "flame.fill",
                    color: SerenityTheme.Colors.warning
                )
                
                StatCard(
                    title: "Longest Streak",
                    value: "\(authService.currentUser?.stats.longestStreak ?? 0) days",
                    icon: "trophy.fill",
                    color: SerenityTheme.Colors.accent
                )
            }
        }
    }
    
    private var achievementsSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Achievements")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: SerenityTheme.Spacing.md) {
                    AchievementBadge(
                        title: "First Session",
                        description: "Complete your first breathing session",
                        icon: "star.fill",
                        isUnlocked: true
                    )
                    
                    AchievementBadge(
                        title: "Week Warrior",
                        description: "Practice for 7 days in a row",
                        icon: "calendar",
                        isUnlocked: false
                    )
                    
                    AchievementBadge(
                        title: "Zen Master",
                        description: "Complete 100 sessions",
                        icon: "brain.head.profile",
                        isUnlocked: false
                    )
                }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
            }
            .padding(.horizontal, -SerenityTheme.Spacing.lg)
        }
    }
    
    private var recentActivitySection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Recent Activity")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            VStack(spacing: SerenityTheme.Spacing.sm) {
                ActivityRow(
                    technique: "4-7-8 Breathing",
                    duration: "5:30",
                    date: "Today",
                    completion: 100
                )
                
                ActivityRow(
                    technique: "Box Breathing",
                    duration: "8:00",
                    date: "Yesterday",
                    completion: 85
                )
                
                ActivityRow(
                    technique: "Equal Breathing",
                    duration: "3:45",
                    date: "2 days ago",
                    completion: 100
                )
            }
        }
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: SerenityTheme.Spacing.sm) {
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(color)
            
            Text(value)
                .font(SerenityTheme.Typography.headline)
                .fontWeight(.bold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            Text(title)
                .font(SerenityTheme.Typography.caption1)
                .foregroundColor(SerenityTheme.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(SerenityTheme.Spacing.md)
        .serenityCardStyle()
    }
}

struct AchievementBadge: View {
    let title: String
    let description: String
    let icon: String
    let isUnlocked: Bool
    
    var body: some View {
        VStack(spacing: SerenityTheme.Spacing.sm) {
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(isUnlocked ? SerenityTheme.Colors.warning : SerenityTheme.Colors.textTertiary)
            
            Text(title)
                .font(SerenityTheme.Typography.callout)
                .fontWeight(.medium)
                .foregroundColor(isUnlocked ? SerenityTheme.Colors.textPrimary : SerenityTheme.Colors.textTertiary)
                .multilineTextAlignment(.center)
            
            Text(description)
                .font(SerenityTheme.Typography.caption2)
                .foregroundColor(SerenityTheme.Colors.textTertiary)
                .multilineTextAlignment(.center)
        }
        .frame(width: 120)
        .padding(SerenityTheme.Spacing.md)
        .background(
            isUnlocked ? SerenityTheme.Colors.cardBackground : SerenityTheme.Colors.cardBackground.opacity(0.5)
        )
        .cornerRadius(SerenityTheme.CornerRadius.medium)
        .overlay(
            RoundedRectangle(cornerRadius: SerenityTheme.CornerRadius.medium)
                .stroke(
                    isUnlocked ? SerenityTheme.Colors.warning.opacity(0.3) : SerenityTheme.Colors.textTertiary.opacity(0.2),
                    lineWidth: 1
                )
        )
    }
}

struct ActivityRow: View {
    let technique: String
    let duration: String
    let date: String
    let completion: Int
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(technique)
                    .font(SerenityTheme.Typography.callout)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)
                
                Text("\(duration) • \(date)")
                    .font(SerenityTheme.Typography.caption1)
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
            }
            
            Spacer()
            
            Text("\(completion)%")
                .font(SerenityTheme.Typography.caption1)
                .foregroundColor(completion == 100 ? SerenityTheme.Colors.success : SerenityTheme.Colors.textSecondary)
        }
        .padding(SerenityTheme.Spacing.md)
        .serenityCardStyle()
    }
}

#Preview {
    ProfileView()
}
