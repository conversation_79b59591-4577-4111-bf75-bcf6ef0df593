//
//  BreathingTechnique.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation

struct BreathingTechnique: Codable, Identifiable {
    let id: UUID
    let name: String
    let description: String
    let category: BreathingCategory
    let difficulty: DifficultyLevel
    let duration: TimeInterval
    let instructions: [String]
    let benefits: [String]
    let isPremium: Bool
    let imageName: String
    let breathingPattern: BreathingPattern
    let localizationKey: String

    var localizedName: String {
        return localizationKey.localized()
    }

    var localizedDescription: String {
        return "\(localizationKey)_desc".localized()
    }
    
    init(name: String, description: String, category: BreathingCategory, difficulty: DifficultyLevel, duration: TimeInterval, instructions: [String], benefits: [String], isPremium: Bool, imageName: String, breathingPattern: BreathingPattern, localizationKey: String) {
        self.id = UUID()
        self.name = name
        self.description = description
        self.category = category
        self.difficulty = difficulty
        self.duration = duration
        self.instructions = instructions
        self.benefits = benefits
        self.isPremium = isPremium
        self.imageName = imageName
        self.breathingPattern = breathingPattern
        self.localizationKey = localizationKey
    }
}

enum BreathingCategory: String, Codable, CaseIterable {
    case quickExercises = "quick_exercises"
    case meditate = "meditate_techniques"
    case insomnia = "insomnia"
    case stressReduction = "stress_reduction"
    case sports = "sports"

    var displayName: String {
        return self.rawValue.localized()
    }
}

enum DifficultyLevel: String, Codable, CaseIterable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"

    var displayName: String {
        return self.rawValue.localized()
    }
}

struct BreathingPattern: Codable {
    let inhaleTime: Double
    let holdTime: Double?
    let exhaleTime: Double
    let pauseTime: Double?
    let cycles: Int
    
    init(inhaleTime: Double, holdTime: Double? = nil, exhaleTime: Double, pauseTime: Double? = nil, cycles: Int) {
        self.inhaleTime = inhaleTime
        self.holdTime = holdTime
        self.exhaleTime = exhaleTime
        self.pauseTime = pauseTime
        self.cycles = cycles
    }
}

// MARK: - Predefined Breathing Techniques
extension BreathingTechnique {
    static let allTechniques: [BreathingTechnique] = [
        // Quick Exercises
        .diaphragmaticBreathing,
        .equalBreathing,
        .alternateNostrilBreathing,
        
        // Advanced
        .completeBreathing,
        
        // Insomnia
        .box444Breathing,
        .breathing478,
        
        // Emotion Regulation
        .box444BreathingEmotion,
        .breathing478Emotion,
        
        // Sports
        .bellowsBreath
    ]
    
    static let diaphragmaticBreathing = BreathingTechnique(
        name: "Diaphragmatic Breathing",
        description: "Deep belly breathing that helps reduce stress and improve focus.",
        category: .quickExercises,
        difficulty: .beginner,
        duration: 300, // 5 minutes
        instructions: [
            "Get Comfortable",
            "Find a comfortable position, either sitting or lying down.",
            "Close Your Eyes",
            "Close your eyes, mouth and bring your attention to your breath.",
            "Inhale",
            "Deeply inhale through your nose, feeling your belly expand.",
            "Exhale",
            "Exhale slowly through your mouth, feeling your belly contract.",
            "Continue Breathing",
            "Continue this pattern for 5-8 minutes, focusing on your breath."
        ],
        benefits: ["Reduces stress", "Improves focus", "Promotes relaxation"],
        isPremium: false,
        imageName: "diaphragmatic_breathing",
        breathingPattern: BreathingPattern(inhaleTime: 4, exhaleTime: 6, cycles: 10),
        localizationKey: "diaphragmatic_breathing"
    )
    
    static let equalBreathing = BreathingTechnique(
        name: "Equal Breathing",
        description: "Calms and soothes the nervous system and helps balance the mind.",
        category: .quickExercises,
        difficulty: .beginner,
        duration: 300,
        instructions: [
            "Get Comfortable",
            "Find a comfortable position, either sitting or lying down.",
            "Close Your Eyes",
            "Close your eyes and bring your attention to your breath.",
            "Inhale",
            "Inhale through your nose for a count of 4.",
            "Exhale",
            "Exhale through your nose for a count of 4.",
            "Continue Breathing",
            "Continue this equal pattern, maintaining the same count for both inhale and exhale."
        ],
        benefits: ["Calms nervous system", "Improves focus", "Balances mind"],
        isPremium: false,
        imageName: "equal_breathing",
        breathingPattern: BreathingPattern(inhaleTime: 4, exhaleTime: 4, cycles: 15),
        localizationKey: "equal_breathing"
    )
    
    static let alternateNostrilBreathing = BreathingTechnique(
        name: "Alternate Nostril Breathing",
        description: "Regulates energy flow and calms the mind and nervous system.",
        category: .quickExercises,
        difficulty: .intermediate,
        duration: 600,
        instructions: [
            "Get Comfortable",
            "Sit comfortably with your spine straight.",
            "Hand Position",
            "Use your right thumb to close your right nostril.",
            "Inhale Left",
            "Inhale slowly through your left nostril.",
            "Switch",
            "Close your left nostril with your ring finger, release your thumb.",
            "Exhale Right",
            "Exhale slowly through your right nostril.",
            "Continue",
            "Continue alternating nostrils for each breath cycle."
        ],
        benefits: ["Regulates energy", "Calms mind", "Balances nervous system"],
        isPremium: false,
        imageName: "alternate_nostril",
        breathingPattern: BreathingPattern(inhaleTime: 4, exhaleTime: 4, cycles: 12),
        localizationKey: "alternate_nostril_breathing"
    )
    
    static let completeBreathing = BreathingTechnique(
        name: "Complete Breathing",
        description: "A comprehensive breathing technique that uses the full capacity of the lungs.",
        category: .meditate,
        difficulty: .advanced,
        duration: 900,
        instructions: [
            "Get Comfortable",
            "Sit or lie down in a comfortable position.",
            "Three-Part Breath",
            "Breathe into your belly, then ribs, then chest.",
            "Full Inhale",
            "Take a complete, deep inhale filling all three areas.",
            "Pause",
            "Hold the breath briefly at the top.",
            "Complete Exhale",
            "Exhale slowly, releasing air from chest, ribs, then belly.",
            "Continue",
            "Repeat this complete breathing pattern."
        ],
        benefits: ["Increases lung capacity", "Reduces anxiety", "Improves energy"],
        isPremium: true,
        imageName: "complete_breathing",
        breathingPattern: BreathingPattern(inhaleTime: 6, holdTime: 2, exhaleTime: 8, cycles: 8),
        localizationKey: "complete_breathing"
    )
    
    static let box444Breathing = BreathingTechnique(
        name: "Box 4-4-4 Breathing",
        description: "Equal parts inhale, hold, and exhale for a balanced breathing pattern.",
        category: .insomnia,
        difficulty: .beginner,
        duration: 480,
        instructions: [
            "Get Comfortable",
            "Find a comfortable position, either sitting or lying down.",
            "Inhale",
            "Inhale through your nose for 4 counts.",
            "Hold",
            "Hold your breath for 4 counts.",
            "Exhale",
            "Exhale through your mouth for 4 counts.",
            "Continue",
            "Repeat this 4-4-4 pattern for 8 minutes."
        ],
        benefits: ["Promotes sleep", "Reduces anxiety", "Calms mind"],
        isPremium: true,
        imageName: "box_breathing",
        breathingPattern: BreathingPattern(inhaleTime: 4, holdTime: 4, exhaleTime: 4, cycles: 20),
        localizationKey: "box_breathing"
    )
    
    static let breathing478 = BreathingTechnique(
        name: "4-7-8 Breathing",
        description: "Inhale for 4, hold for 7, exhale for 8 to promote deep relaxation.",
        category: .insomnia,
        difficulty: .intermediate,
        duration: 240,
        instructions: [
            "Get Comfortable",
            "Sit or lie down comfortably.",
            "Exhale Completely",
            "Exhale completely through your mouth.",
            "Inhale",
            "Inhale through your nose for 4 counts.",
            "Hold",
            "Hold your breath for 7 counts.",
            "Exhale",
            "Exhale through your mouth for 8 counts.",
            "Repeat",
            "Repeat this cycle 4 times initially."
        ],
        benefits: ["Promotes deep sleep", "Reduces stress", "Calms nervous system"],
        isPremium: true,
        imageName: "478_breathing",
        breathingPattern: BreathingPattern(inhaleTime: 4, holdTime: 7, exhaleTime: 8, cycles: 4),
        localizationKey: "breathing_478"
    )
    
    static let box444BreathingEmotion = BreathingTechnique(
        name: "Box 4-4-4 Breathing",
        description: "Structured breathing to help regulate emotions and find balance.",
        category: .stressReduction,
        difficulty: .beginner,
        duration: 600,
        instructions: [
            "Get Comfortable",
            "Find a comfortable position and close your eyes.",
            "Focus",
            "Bring your attention to your current emotional state.",
            "Inhale",
            "Inhale slowly for 4 counts, imagining calm entering your body.",
            "Hold",
            "Hold for 4 counts, letting the calm settle.",
            "Exhale",
            "Exhale for 4 counts, releasing tension and stress.",
            "Continue",
            "Continue this pattern, focusing on emotional balance."
        ],
        benefits: ["Regulates emotions", "Reduces stress", "Improves emotional balance"],
        isPremium: true,
        imageName: "box_breathing_emotion",
        breathingPattern: BreathingPattern(inhaleTime: 4, holdTime: 4, exhaleTime: 4, cycles: 25),
        localizationKey: "box_breathing"
    )
    
    static let breathing478Emotion = BreathingTechnique(
        name: "4-7-8 Breathing",
        description: "Deep relaxation technique for emotional regulation and stress relief.",
        category: .stressReduction,
        difficulty: .intermediate,
        duration: 360,
        instructions: [
            "Prepare",
            "Sit comfortably and acknowledge your current emotions.",
            "Exhale",
            "Exhale completely, releasing any tension.",
            "Inhale",
            "Inhale for 4 counts, breathing in peace.",
            "Hold",
            "Hold for 7 counts, letting peace fill your body.",
            "Exhale",
            "Exhale for 8 counts, releasing emotional tension.",
            "Repeat",
            "Continue for 6 minutes, focusing on emotional release."
        ],
        benefits: ["Emotional regulation", "Stress relief", "Mental clarity"],
        isPremium: true,
        imageName: "478_breathing_emotion",
        breathingPattern: BreathingPattern(inhaleTime: 4, holdTime: 7, exhaleTime: 8, cycles: 6),
        localizationKey: "breathing_478"
    )
    
    static let bellowsBreath = BreathingTechnique(
        name: "Bellows Breath",
        description: "Energizing breath work to increase alertness and invigorate the body.",
        category: .sports,
        difficulty: .advanced,
        duration: 300,
        instructions: [
            "Prepare",
            "Sit up straight with your spine erect.",
            "Hand Position",
            "Place your hands on your knees.",
            "Rapid Breathing",
            "Begin rapid, forceful breathing through your nose.",
            "Equal Inhale/Exhale",
            "Make inhales and exhales equal in force and duration.",
            "Continue",
            "Continue for 30 seconds, then rest.",
            "Repeat",
            "Repeat 3-5 rounds with rest periods between."
        ],
        benefits: ["Increases energy", "Improves alertness", "Invigorates body"],
        isPremium: true,
        imageName: "bellows_breath",
        breathingPattern: BreathingPattern(inhaleTime: 1, exhaleTime: 1, cycles: 30),
        localizationKey: "bellows_breath"
    )
}
