//
//  NotificationManager.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import UserNotifications
import Combine

class NotificationManager: NSObject, ObservableObject {
    @Published var authorizationStatus: UNAuthorizationStatus = .notDetermined
    
    override init() {
        super.init()
        UNUserNotificationCenter.current().delegate = self
        checkAuthorizationStatus()
    }
    
    // MARK: - Authorization
    
    func requestAuthorization() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            DispatchQueue.main.async {
                self.checkAuthorizationStatus()
            }
        }
    }
    
    func checkAuthorizationStatus() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                self.authorizationStatus = settings.authorizationStatus
            }
        }
    }
    
    // MARK: - Scheduling Notifications
    
    func scheduleNotifications(
        dailyRemindersEnabled: Bool,
        morningTime: Date,
        eveningTime: Date,
        weekendEnabled: Bool
    ) {
        // Remove existing notifications
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
        
        guard dailyRemindersEnabled else { return }
        
        // Schedule morning reminders
        scheduleDailyReminder(
            identifier: "morning_reminder",
            title: "Good Morning! 🌅",
            body: "Start your day with a calming breathing exercise",
            time: morningTime,
            includeWeekends: weekendEnabled
        )
        
        // Schedule evening reminders
        scheduleDailyReminder(
            identifier: "evening_reminder",
            title: "Wind Down 🌙",
            body: "Take a moment to breathe and relax before bed",
            time: eveningTime,
            includeWeekends: weekendEnabled
        )
        
        // Schedule streak reminders
        scheduleStreakReminders()
        
        // Schedule weekly motivation
        scheduleWeeklyMotivation()
    }
    
    private func scheduleDailyReminder(
        identifier: String,
        title: String,
        body: String,
        time: Date,
        includeWeekends: Bool
    ) {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: time)
        let minute = calendar.component(.minute, from: time)
        
        let weekdays = includeWeekends ? [1, 2, 3, 4, 5, 6, 7] : [2, 3, 4, 5, 6] // Sunday = 1, Monday = 2, etc.
        
        for (index, weekday) in weekdays.enumerated() {
            var dateComponents = DateComponents()
            dateComponents.hour = hour
            dateComponents.minute = minute
            dateComponents.weekday = weekday
            
            let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: true)
            
            let content = UNMutableNotificationContent()
            content.title = title
            content.body = body
            content.sound = .default
            content.badge = 1
            
            let request = UNNotificationRequest(
                identifier: "\(identifier)_\(index)",
                content: content,
                trigger: trigger
            )
            
            UNUserNotificationCenter.current().add(request)
        }
    }
    
    private func scheduleStreakReminders() {
        // Remind users about their streak if they haven't practiced
        let content = UNMutableNotificationContent()
        content.title = "Don't Break Your Streak! 🔥"
        content.body = "You're doing great! Keep your breathing practice going."
        content.sound = .default
        
        // Schedule for 7 PM if no session today
        var dateComponents = DateComponents()
        dateComponents.hour = 19
        dateComponents.minute = 0
        
        let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: true)
        let request = UNNotificationRequest(identifier: "streak_reminder", content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request)
    }
    
    private func scheduleWeeklyMotivation() {
        // Weekly motivation on Sunday evening
        let content = UNMutableNotificationContent()
        content.title = "Weekly Reflection 🧘‍♀️"
        content.body = "How was your breathing practice this week? Let's prepare for the week ahead."
        content.sound = .default
        
        var dateComponents = DateComponents()
        dateComponents.weekday = 1 // Sunday
        dateComponents.hour = 18
        dateComponents.minute = 0
        
        let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: true)
        let request = UNNotificationRequest(identifier: "weekly_motivation", content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request)
    }
    
    // MARK: - Achievement Notifications
    
    func scheduleAchievementNotification(title: String, body: String) {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.sound = .default
        content.badge = 1
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        let request = UNNotificationRequest(identifier: UUID().uuidString, content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request)
    }
    
    // MARK: - Test Notification
    
    func scheduleTestNotification() {
        let content = UNMutableNotificationContent()
        content.title = "Test Notification 🧪"
        content.body = "This is a test notification from Serenity. Your notifications are working!"
        content.sound = .default
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 2, repeats: false)
        let request = UNNotificationRequest(identifier: "test_notification", content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Error scheduling test notification: \(error)")
            }
        }
    }
    
    // MARK: - Utility
    
    func removeAllNotifications() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
        UNUserNotificationCenter.current().removeAllDeliveredNotifications()
    }
    
    func getPendingNotifications(completion: @escaping ([UNNotificationRequest]) -> Void) {
        UNUserNotificationCenter.current().getPendingNotificationRequests(completionHandler: completion)
    }
}

// MARK: - UNUserNotificationCenterDelegate

extension NotificationManager: UNUserNotificationCenterDelegate {
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // Show notification even when app is in foreground
        if #available(iOS 14.0, *) {
            completionHandler([.banner, .badge, .sound])
        } else {
            completionHandler([.alert, .badge, .sound])
        }
    }
    
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        // Handle notification tap
        let identifier = response.notification.request.identifier
        
        if identifier.contains("morning_reminder") || identifier.contains("evening_reminder") {
            // Navigate to breathing exercises
            NotificationCenter.default.post(name: .openBreathingExercises, object: nil)
        } else if identifier.contains("streak_reminder") {
            // Navigate to home/stats
            NotificationCenter.default.post(name: .openHome, object: nil)
        }
        
        completionHandler()
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let openBreathingExercises = Notification.Name("openBreathingExercises")
    static let openHome = Notification.Name("openHome")
}
