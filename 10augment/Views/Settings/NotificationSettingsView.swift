//
//  NotificationSettingsView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI
import UserNotifications

struct NotificationSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var notificationManager = NotificationManager()
    
    // Notification Permission
    @State private var notificationsEnabled = false
    
    // Reminder Settings
    @State private var dailyRemindersEnabled = true
    @State private var morningReminderTime = Date()
    @State private var eveningReminderTime = Date()
    @State private var weekendRemindersEnabled = true
    
    // Content Preferences
    @State private var breathingRemindersEnabled = true
    @State private var meditationRemindersEnabled = true
    @State private var sleepRemindersEnabled = true
    @State private var streakRemindersEnabled = true
    @State private var achievementNotificationsEnabled = true
    
    // App Updates
    @State private var appUpdatesEnabled = true
    @State private var newFeaturesEnabled = true
    @State private var tipsAndInsightsEnabled = true
    
    // Marketing
    @State private var promotionalOffersEnabled = false
    @State private var newsletterEnabled = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: SerenityTheme.Spacing.xl) {
                    // Header
                    headerSection
                    
                    // Permission Status
                    permissionSection
                    
                    // Daily Reminders
                    dailyRemindersSection
                    
                    // Content Preferences
                    contentPreferencesSection
                    
                    // App Updates
                    appUpdatesSection
                    
                    // Marketing
                    marketingSection
                    
                    // Quick Actions
                    quickActionsSection
                }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
                .padding(.vertical, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationTitle("Notifications")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        saveSettings()
                        dismiss()
                    }
                    .foregroundColor(SerenityTheme.Colors.primary)
                }
            }
        }
        .onAppear {
            loadSettings()
            checkNotificationPermission()
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: SerenityTheme.Spacing.md) {
            Image(systemName: "bell.circle.fill")
                .font(.system(size: 50))
                .foregroundColor(SerenityTheme.Colors.primary)
            
            Text("Notification Settings")
                .font(SerenityTheme.Typography.title2)
                .fontWeight(.bold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            Text("Customize when and how you receive notifications from Serenity")
                .font(SerenityTheme.Typography.body)
                .foregroundColor(SerenityTheme.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
    }
    
    private var permissionSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Notification Permission")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            VStack(spacing: SerenityTheme.Spacing.sm) {
                NotificationToggleRow(
                    title: "Allow Notifications",
                    subtitle: notificationsEnabled ? "Notifications are enabled" : "Enable to receive reminders and updates",
                    isOn: $notificationsEnabled,
                    action: {
                        if !notificationsEnabled {
                            requestNotificationPermission()
                        } else {
                            openAppSettings()
                        }
                    }
                )
                
                if !notificationsEnabled {
                    NotificationInfoCard(
                        icon: "exclamationmark.triangle",
                        title: "Notifications Disabled",
                        description: "To receive breathing reminders and updates, please enable notifications in your device settings.",
                        actionTitle: "Open Settings",
                        action: openAppSettings
                    )
                }
            }
        }
    }

    private var dailyRemindersSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Daily Reminders")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            VStack(spacing: SerenityTheme.Spacing.sm) {
                NotificationToggleRow(
                    title: "Daily Practice Reminders",
                    subtitle: "Get reminded to practice breathing exercises",
                    isOn: $dailyRemindersEnabled
                )

                if dailyRemindersEnabled {
                    NotificationTimeRow(
                        title: "Morning Reminder",
                        time: $morningReminderTime,
                        isEnabled: true
                    )

                    NotificationTimeRow(
                        title: "Evening Reminder",
                        time: $eveningReminderTime,
                        isEnabled: true
                    )

                    NotificationToggleRow(
                        title: "Weekend Reminders",
                        subtitle: "Include Saturday and Sunday",
                        isOn: $weekendRemindersEnabled
                    )
                }
            }
        }
    }

    private var contentPreferencesSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Content Preferences")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            VStack(spacing: SerenityTheme.Spacing.sm) {
                NotificationToggleRow(
                    title: "Breathing Exercise Reminders",
                    subtitle: "Suggestions for breathing techniques",
                    isOn: $breathingRemindersEnabled
                )

                NotificationToggleRow(
                    title: "Meditation Reminders",
                    subtitle: "Mindfulness and meditation prompts",
                    isOn: $meditationRemindersEnabled
                )

                NotificationToggleRow(
                    title: "Sleep Reminders",
                    subtitle: "Bedtime breathing exercise suggestions",
                    isOn: $sleepRemindersEnabled
                )

                NotificationToggleRow(
                    title: "Streak Reminders",
                    subtitle: "Maintain your practice streak",
                    isOn: $streakRemindersEnabled
                )

                NotificationToggleRow(
                    title: "Achievement Notifications",
                    subtitle: "Celebrate your milestones",
                    isOn: $achievementNotificationsEnabled
                )
            }
        }
    }

    private var appUpdatesSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("App Updates")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            VStack(spacing: SerenityTheme.Spacing.sm) {
                NotificationToggleRow(
                    title: "App Updates",
                    subtitle: "New versions and bug fixes",
                    isOn: $appUpdatesEnabled
                )

                NotificationToggleRow(
                    title: "New Features",
                    subtitle: "Learn about new breathing techniques",
                    isOn: $newFeaturesEnabled
                )

                NotificationToggleRow(
                    title: "Tips & Insights",
                    subtitle: "Wellness tips and breathing insights",
                    isOn: $tipsAndInsightsEnabled
                )
            }
        }
    }

    private var marketingSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Marketing & Promotions")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            VStack(spacing: SerenityTheme.Spacing.sm) {
                NotificationToggleRow(
                    title: "Promotional Offers",
                    subtitle: "Special discounts and premium offers",
                    isOn: $promotionalOffersEnabled
                )

                NotificationToggleRow(
                    title: "Newsletter",
                    subtitle: "Monthly wellness newsletter",
                    isOn: $newsletterEnabled
                )
            }
        }
    }

    private var quickActionsSection: some View {
        VStack(spacing: SerenityTheme.Spacing.md) {
            Button(action: {
                scheduleTestNotification()
            }) {
                HStack {
                    Image(systemName: "bell.badge")
                        .font(.system(size: 16))

                    Text("Send Test Notification")
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.medium)
                }
                .foregroundColor(SerenityTheme.Colors.primary)
                .frame(maxWidth: .infinity)
                .padding(.vertical, SerenityTheme.Spacing.md)
                .background(SerenityTheme.Colors.cardBackground)
                .cornerRadius(SerenityTheme.CornerRadius.small)
                .overlay(
                    RoundedRectangle(cornerRadius: SerenityTheme.CornerRadius.small)
                        .stroke(SerenityTheme.Colors.primary, lineWidth: 1)
                )
            }

            Button(action: {
                resetToDefaults()
            }) {
                HStack {
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: 16))

                    Text("Reset to Defaults")
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.medium)
                }
                .foregroundColor(SerenityTheme.Colors.textSecondary)
                .frame(maxWidth: .infinity)
                .padding(.vertical, SerenityTheme.Spacing.md)
                .background(SerenityTheme.Colors.cardBackground)
                .cornerRadius(SerenityTheme.CornerRadius.small)
            }
        }
    }

    // MARK: - Helper Functions

    private func loadSettings() {
        // Load settings from UserDefaults
        dailyRemindersEnabled = UserDefaults.standard.bool(forKey: "dailyRemindersEnabled")
        breathingRemindersEnabled = UserDefaults.standard.bool(forKey: "breathingRemindersEnabled")
        meditationRemindersEnabled = UserDefaults.standard.bool(forKey: "meditationRemindersEnabled")
        sleepRemindersEnabled = UserDefaults.standard.bool(forKey: "sleepRemindersEnabled")
        streakRemindersEnabled = UserDefaults.standard.bool(forKey: "streakRemindersEnabled")
        achievementNotificationsEnabled = UserDefaults.standard.bool(forKey: "achievementNotificationsEnabled")
        appUpdatesEnabled = UserDefaults.standard.bool(forKey: "appUpdatesEnabled")
        newFeaturesEnabled = UserDefaults.standard.bool(forKey: "newFeaturesEnabled")
        tipsAndInsightsEnabled = UserDefaults.standard.bool(forKey: "tipsAndInsightsEnabled")
        promotionalOffersEnabled = UserDefaults.standard.bool(forKey: "promotionalOffersEnabled")
        newsletterEnabled = UserDefaults.standard.bool(forKey: "newsletterEnabled")
        weekendRemindersEnabled = UserDefaults.standard.bool(forKey: "weekendRemindersEnabled")

        // Load time settings
        if let morningData = UserDefaults.standard.data(forKey: "morningReminderTime"),
           let morningTime = try? JSONDecoder().decode(Date.self, from: morningData) {
            morningReminderTime = morningTime
        } else {
            morningReminderTime = Calendar.current.date(bySettingHour: 8, minute: 0, second: 0, of: Date()) ?? Date()
        }

        if let eveningData = UserDefaults.standard.data(forKey: "eveningReminderTime"),
           let eveningTime = try? JSONDecoder().decode(Date.self, from: eveningData) {
            eveningReminderTime = eveningTime
        } else {
            eveningReminderTime = Calendar.current.date(bySettingHour: 20, minute: 0, second: 0, of: Date()) ?? Date()
        }
    }

    private func saveSettings() {
        // Save settings to UserDefaults
        UserDefaults.standard.set(dailyRemindersEnabled, forKey: "dailyRemindersEnabled")
        UserDefaults.standard.set(breathingRemindersEnabled, forKey: "breathingRemindersEnabled")
        UserDefaults.standard.set(meditationRemindersEnabled, forKey: "meditationRemindersEnabled")
        UserDefaults.standard.set(sleepRemindersEnabled, forKey: "sleepRemindersEnabled")
        UserDefaults.standard.set(streakRemindersEnabled, forKey: "streakRemindersEnabled")
        UserDefaults.standard.set(achievementNotificationsEnabled, forKey: "achievementNotificationsEnabled")
        UserDefaults.standard.set(appUpdatesEnabled, forKey: "appUpdatesEnabled")
        UserDefaults.standard.set(newFeaturesEnabled, forKey: "newFeaturesEnabled")
        UserDefaults.standard.set(tipsAndInsightsEnabled, forKey: "tipsAndInsightsEnabled")
        UserDefaults.standard.set(promotionalOffersEnabled, forKey: "promotionalOffersEnabled")
        UserDefaults.standard.set(newsletterEnabled, forKey: "newsletterEnabled")
        UserDefaults.standard.set(weekendRemindersEnabled, forKey: "weekendRemindersEnabled")

        // Save time settings
        if let morningData = try? JSONEncoder().encode(morningReminderTime) {
            UserDefaults.standard.set(morningData, forKey: "morningReminderTime")
        }
        if let eveningData = try? JSONEncoder().encode(eveningReminderTime) {
            UserDefaults.standard.set(eveningData, forKey: "eveningReminderTime")
        }

        // Schedule notifications
        notificationManager.scheduleNotifications(
            dailyRemindersEnabled: dailyRemindersEnabled,
            morningTime: morningReminderTime,
            eveningTime: eveningReminderTime,
            weekendEnabled: weekendRemindersEnabled
        )
    }

    private func checkNotificationPermission() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                self.notificationsEnabled = settings.authorizationStatus == .authorized
            }
        }
    }

    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, _ in
            DispatchQueue.main.async {
                self.notificationsEnabled = granted
            }
        }
    }

    private func openAppSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }

    private func scheduleTestNotification() {
        notificationManager.scheduleTestNotification()
    }

    private func resetToDefaults() {
        dailyRemindersEnabled = true
        breathingRemindersEnabled = true
        meditationRemindersEnabled = true
        sleepRemindersEnabled = true
        streakRemindersEnabled = true
        achievementNotificationsEnabled = true
        appUpdatesEnabled = true
        newFeaturesEnabled = true
        tipsAndInsightsEnabled = true
        promotionalOffersEnabled = false
        newsletterEnabled = false
        weekendRemindersEnabled = true

        morningReminderTime = Calendar.current.date(bySettingHour: 8, minute: 0, second: 0, of: Date()) ?? Date()
        eveningReminderTime = Calendar.current.date(bySettingHour: 20, minute: 0, second: 0, of: Date()) ?? Date()
    }
}

// MARK: - Supporting Components

struct NotificationToggleRow: View {
    let title: String
    let subtitle: String
    @Binding var isOn: Bool
    var action: (() -> Void)? = nil

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(SerenityTheme.Typography.callout)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)

                Text(subtitle)
                    .font(SerenityTheme.Typography.caption1)
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
            }

            Spacer()

            Toggle("", isOn: $isOn)
                .toggleStyle(SwitchToggleStyle(tint: SerenityTheme.Colors.primary))
                .onChange(of: isOn) { _, newValue in
                    action?()
                }
        }
        .padding(SerenityTheme.Spacing.md)
        .serenityCardStyle()
    }
}

struct NotificationTimeRow: View {
    let title: String
    @Binding var time: Date
    let isEnabled: Bool

    var body: some View {
        HStack {
            Text(title)
                .font(SerenityTheme.Typography.callout)
                .foregroundColor(SerenityTheme.Colors.textPrimary)

            Spacer()

            DatePicker("", selection: $time, displayedComponents: .hourAndMinute)
                .labelsHidden()
                .disabled(!isEnabled)
        }
        .padding(SerenityTheme.Spacing.md)
        .serenityCardStyle()
        .opacity(isEnabled ? 1.0 : 0.6)
    }
}

struct NotificationInfoCard: View {
    let icon: String
    let title: String
    let description: String
    let actionTitle: String
    let action: () -> Void

    var body: some View {
        VStack(spacing: SerenityTheme.Spacing.md) {
            HStack(spacing: SerenityTheme.Spacing.md) {
                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(SerenityTheme.Colors.warning)

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.medium)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)

                    Text(description)
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                }

                Spacer()
            }

            Button(action: action) {
                Text(actionTitle)
                    .font(SerenityTheme.Typography.callout)
                    .fontWeight(.medium)
                    .foregroundColor(SerenityTheme.Colors.primary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, SerenityTheme.Spacing.sm)
                    .background(SerenityTheme.Colors.primary.opacity(0.1))
                    .cornerRadius(SerenityTheme.CornerRadius.small)
            }
        }
        .padding(SerenityTheme.Spacing.md)
        .background(SerenityTheme.Colors.warning.opacity(0.1))
        .cornerRadius(SerenityTheme.CornerRadius.medium)
        .overlay(
            RoundedRectangle(cornerRadius: SerenityTheme.CornerRadius.medium)
                .stroke(SerenityTheme.Colors.warning.opacity(0.3), lineWidth: 1)
        )
    }
}

#Preview {
    NotificationSettingsView()
}
