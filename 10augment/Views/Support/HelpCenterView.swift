//
//  HelpCenterView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct HelpCenterView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var searchText = ""
    
    private let helpCategories = [
        HelpCategory(
            title: "Getting Started",
            icon: "play.circle",
            articles: [
                HelpArticle(title: "How to use breathing exercises", content: "Learn the basics of using Serenity's breathing techniques for maximum benefit."),
                HelpArticle(title: "Setting up your profile", content: "Customize your profile and preferences for a personalized experience."),
                HelpArticle(title: "Understanding breathing patterns", content: "Discover different breathing patterns and their specific benefits.")
            ]
        ),
        HelpCategory(
            title: "Breathing Techniques",
            icon: "wind",
            articles: [
                HelpArticle(title: "4-7-8 Breathing for sleep", content: "Master this powerful technique for better sleep and relaxation."),
                HelpArticle(title: "Box breathing for stress", content: "Learn how to use box breathing to manage stress and anxiety."),
                HelpArticle(title: "Diaphragmatic breathing basics", content: "Understand the foundation of all breathing practices.")
            ]
        ),
        HelpCategory(
            title: "Premium Features",
            icon: "crown",
            articles: [
                HelpArticle(title: "What's included in Premium", content: "Explore all the advanced features available with Premium subscription."),
                HelpArticle(title: "Managing your subscription", content: "Learn how to upgrade, downgrade, or cancel your subscription."),
                HelpArticle(title: "Restoring purchases", content: "How to restore your Premium features on a new device.")
            ]
        ),
        HelpCategory(
            title: "Troubleshooting",
            icon: "wrench",
            articles: [
                HelpArticle(title: "App not responding", content: "Steps to resolve common app performance issues."),
                HelpArticle(title: "Audio not playing", content: "Fix sound issues during breathing exercises."),
                HelpArticle(title: "Sync issues", content: "Resolve problems with data synchronization across devices.")
            ]
        )
    ]
    
    private var filteredCategories: [HelpCategory] {
        if searchText.isEmpty {
            return helpCategories
        } else {
            return helpCategories.compactMap { category in
                let filteredArticles = category.articles.filter {
                    $0.title.localizedCaseInsensitiveContains(searchText) ||
                    $0.content.localizedCaseInsensitiveContains(searchText)
                }
                if !filteredArticles.isEmpty {
                    return HelpCategory(title: category.title, icon: category.icon, articles: filteredArticles)
                }
                return nil
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: SerenityTheme.Spacing.xl) {
                    // Header
                    headerSection
                    
                    // Search Bar
                    searchSection
                    
                    // Help Categories
                    categoriesSection
                }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
                .padding(.vertical, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationTitle("help_center".localized())
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("done".localized()) {
                        dismiss()
                    }
                    .foregroundColor(SerenityTheme.Colors.primary)
                }
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: SerenityTheme.Spacing.md) {
            Image(systemName: "questionmark.circle.fill")
                .font(.system(size: 50))
                .foregroundColor(SerenityTheme.Colors.primary)
            
            Text("how_can_we_help".localized())
                .font(SerenityTheme.Typography.title2)
                .fontWeight(.bold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
                .multilineTextAlignment(.center)

            Text("find_answers_questions".localized())
                .font(SerenityTheme.Typography.body)
                .foregroundColor(SerenityTheme.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
    }
    
    private var searchSection: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(SerenityTheme.Colors.textTertiary)
            
            TextField("Search help articles", text: $searchText)
                .font(SerenityTheme.Typography.body)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            if !searchText.isEmpty {
                Button(action: { searchText = "" }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(SerenityTheme.Colors.textTertiary)
                }
            }
        }
        .padding(SerenityTheme.Spacing.md)
        .background(SerenityTheme.Colors.cardBackground)
        .cornerRadius(SerenityTheme.CornerRadius.small)
    }
    
    private var categoriesSection: some View {
        VStack(spacing: SerenityTheme.Spacing.lg) {
            ForEach(filteredCategories, id: \.title) { category in
                HelpCategoryCard(category: category)
            }
        }
    }
}

struct HelpCategory {
    let title: String
    let icon: String
    let articles: [HelpArticle]
}

struct HelpArticle {
    let title: String
    let content: String
}

struct HelpCategoryCard: View {
    let category: HelpCategory
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isExpanded.toggle()
                }
            }) {
                HStack {
                    Image(systemName: category.icon)
                        .font(.system(size: 20))
                        .foregroundColor(SerenityTheme.Colors.primary)
                        .frame(width: 30)
                    
                    Text(category.title)
                        .font(SerenityTheme.Typography.headline)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                    
                    Spacer()
                    
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(SerenityTheme.Colors.textTertiary)
                }
                .padding(SerenityTheme.Spacing.md)
            }
            
            if isExpanded {
                VStack(spacing: SerenityTheme.Spacing.sm) {
                    ForEach(category.articles, id: \.title) { article in
                        HelpArticleRow(article: article)
                    }
                }
                .padding(.horizontal, SerenityTheme.Spacing.md)
                .padding(.bottom, SerenityTheme.Spacing.md)
            }
        }
        .serenityCardStyle()
    }
}

struct HelpArticleRow: View {
    let article: HelpArticle
    @State private var showingDetail = false
    
    var body: some View {
        Button(action: {
            showingDetail = true
        }) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(article.title)
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.medium)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                        .multilineTextAlignment(.leading)
                    
                    Text(article.content)
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(SerenityTheme.Colors.textTertiary)
            }
            .padding(.vertical, SerenityTheme.Spacing.sm)
        }
        .sheet(isPresented: $showingDetail) {
            HelpArticleDetailView(article: article)
        }
    }
}

struct HelpArticleDetailView: View {
    let article: HelpArticle
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: SerenityTheme.Spacing.lg) {
                    Text(article.content)
                        .font(SerenityTheme.Typography.body)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                    
                    // Additional detailed content would go here
                    Text("for_more_detailed_information".localized())
                        .font(SerenityTheme.Typography.callout)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                        .padding(.top, SerenityTheme.Spacing.lg)
                }
                .padding(SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationTitle(article.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("done".localized()) {
                        dismiss()
                    }
                    .foregroundColor(SerenityTheme.Colors.primary)
                }
            }
        }
    }
}

#Preview {
    HelpCenterView()
}
