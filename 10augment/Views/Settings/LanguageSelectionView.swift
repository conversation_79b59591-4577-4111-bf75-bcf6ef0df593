//
//  LanguageSelectionView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct LanguageSelectionView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var localizationManager = LocalizationManager.shared
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: SerenityTheme.Spacing.xl) {
                    // Header
                    headerSection
                    
                    // Language Options
                    languageOptionsSection
                }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
                .padding(.vertical, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationTitle("language".localized())
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("done".localized()) {
                        dismiss()
                    }
                    .foregroundColor(SerenityTheme.Colors.primary)
                }
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: SerenityTheme.Spacing.md) {
            Image(systemName: "globe")
                .font(.system(size: 50))
                .foregroundColor(SerenityTheme.Colors.primary)
            
            Text("language".localized())
                .font(SerenityTheme.Typography.title2)
                .fontWeight(.bold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            Text("Choose your preferred language for the app")
                .font(SerenityTheme.Typography.body)
                .foregroundColor(SerenityTheme.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
    }
    
    private var languageOptionsSection: some View {
        VStack(spacing: SerenityTheme.Spacing.sm) {
            ForEach(Language.allCases) { language in
                LanguageRow(
                    language: language,
                    isSelected: localizationManager.currentLanguage == language,
                    action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            localizationManager.setLanguage(language)
                        }
                    }
                )
            }
        }
    }
}

struct LanguageRow: View {
    let language: Language
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: SerenityTheme.Spacing.md) {
                // Flag
                Text(language.flag)
                    .font(.system(size: 24))
                
                // Language Info
                VStack(alignment: .leading, spacing: 2) {
                    Text(language.displayName)
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.medium)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                    
                    Text(language.nativeName)
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                }
                
                Spacer()
                
                // Selection Indicator
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(SerenityTheme.Colors.primary)
                } else {
                    Image(systemName: "circle")
                        .font(.system(size: 20))
                        .foregroundColor(SerenityTheme.Colors.textTertiary)
                }
            }
            .padding(SerenityTheme.Spacing.md)
            .background(
                isSelected ? 
                SerenityTheme.Colors.primary.opacity(0.1) : 
                SerenityTheme.Colors.cardBackground
            )
            .cornerRadius(SerenityTheme.CornerRadius.medium)
            .overlay(
                RoundedRectangle(cornerRadius: SerenityTheme.CornerRadius.medium)
                    .stroke(
                        isSelected ? 
                        SerenityTheme.Colors.primary : 
                        Color.clear,
                        lineWidth: 2
                    )
            )
        }
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

#Preview {
    LanguageSelectionView()
}
