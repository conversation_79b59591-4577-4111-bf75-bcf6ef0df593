//
//  BreathingTimer.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import Combine

enum BreathingPhase: CaseIterable {
    case inhale
    case hold
    case exhale
    case pause
    
    var instruction: String {
        switch self {
        case .inhale:
            return "Breathe In"
        case .hold:
            return "Hold"
        case .exhale:
            return "Breathe Out"
        case .pause:
            return "Pause"
        }
    }
    
    var iconName: String {
        switch self {
        case .inhale:
            return "arrow.up.circle"
        case .hold:
            return "pause.circle"
        case .exhale:
            return "arrow.down.circle"
        case .pause:
            return "stop.circle"
        }
    }
}

class BreathingTimer: ObservableObject {
    @Published var currentPhase: BreathingPhase = .inhale
    @Published var currentCycle: Int = 1
    @Published var timeRemaining: Double = 0
    @Published var phaseProgress: Double = 0
    @Published var overallProgress: Double = 0
    @Published var isRunning: Bool = false
    @Published var isCompleted: Bool = false
    
    private var timer: Timer?
    private var pattern: BreathingPattern?
    private var totalCycles: Int = 0
    private var currentPhaseTime: Double = 0
    private var totalPhaseTime: Double = 0
    
    func setup(with pattern: BreathingPattern) {
        self.pattern = pattern
        self.totalCycles = pattern.cycles
        self.currentCycle = 1
        self.currentPhase = .inhale
        self.isCompleted = false
        
        setCurrentPhaseTime()
        updateProgress()
    }
    
    func start() {
        guard !isRunning else { return }
        isRunning = true
        startTimer()
    }
    
    func pause() {
        isRunning = false
        timer?.invalidate()
        timer = nil
    }
    
    func resume() {
        guard !isRunning else { return }
        isRunning = true
        startTimer()
    }
    
    func stop() {
        pause()
        reset()
    }
    
    private func reset() {
        currentCycle = 1
        currentPhase = .inhale
        isCompleted = false
        setCurrentPhaseTime()
        updateProgress()
    }
    
    private func startTimer() {
        timer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
            self.tick()
        }
    }
    
    private func tick() {
        timeRemaining -= 0.1
        
        if timeRemaining <= 0 {
            nextPhase()
        }
        
        updateProgress()
    }
    
    private func nextPhase() {
        switch currentPhase {
        case .inhale:
            if pattern?.holdTime != nil && pattern!.holdTime! > 0 {
                currentPhase = .hold
            } else {
                currentPhase = .exhale
            }
        case .hold:
            currentPhase = .exhale
        case .exhale:
            if pattern?.pauseTime != nil && pattern!.pauseTime! > 0 {
                currentPhase = .pause
            } else {
                nextCycle()
            }
        case .pause:
            nextCycle()
        }
        
        setCurrentPhaseTime()
    }
    
    private func nextCycle() {
        if currentCycle >= totalCycles {
            completeExercise()
        } else {
            currentCycle += 1
            currentPhase = .inhale
            setCurrentPhaseTime()
        }
    }
    
    private func completeExercise() {
        pause()
        isCompleted = true
        overallProgress = 1.0
    }
    
    private func setCurrentPhaseTime() {
        guard let pattern = pattern else { return }
        
        switch currentPhase {
        case .inhale:
            totalPhaseTime = pattern.inhaleTime
        case .hold:
            totalPhaseTime = pattern.holdTime ?? 0
        case .exhale:
            totalPhaseTime = pattern.exhaleTime
        case .pause:
            totalPhaseTime = pattern.pauseTime ?? 0
        }
        
        timeRemaining = totalPhaseTime
        currentPhaseTime = totalPhaseTime
    }
    
    private func updateProgress() {
        // Phase progress
        if totalPhaseTime > 0 {
            phaseProgress = 1.0 - (timeRemaining / totalPhaseTime)
        } else {
            phaseProgress = 0
        }
        
        // Overall progress
        let completedCycles = currentCycle - 1
        let currentCycleProgress = getCurrentCycleProgress()
        overallProgress = (Double(completedCycles) + currentCycleProgress) / Double(totalCycles)
    }
    
    private func getCurrentCycleProgress() -> Double {
        guard let pattern = pattern else { return 0 }
        
        let totalCycleTime = pattern.inhaleTime + 
                           (pattern.holdTime ?? 0) + 
                           pattern.exhaleTime + 
                           (pattern.pauseTime ?? 0)
        
        var elapsedCycleTime: Double = 0
        
        switch currentPhase {
        case .inhale:
            elapsedCycleTime = pattern.inhaleTime - timeRemaining
        case .hold:
            elapsedCycleTime = pattern.inhaleTime + ((pattern.holdTime ?? 0) - timeRemaining)
        case .exhale:
            elapsedCycleTime = pattern.inhaleTime + (pattern.holdTime ?? 0) + (pattern.exhaleTime - timeRemaining)
        case .pause:
            elapsedCycleTime = pattern.inhaleTime + (pattern.holdTime ?? 0) + pattern.exhaleTime + ((pattern.pauseTime ?? 0) - timeRemaining)
        }
        
        return totalCycleTime > 0 ? elapsedCycleTime / totalCycleTime : 0
    }
    
    deinit {
        timer?.invalidate()
    }
}
