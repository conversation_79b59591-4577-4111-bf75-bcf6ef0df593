//
//  MeditateView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct MeditateView: View {
    var body: some View {
        NavigationView {
            VStack(spacing: SerenityTheme.Spacing.xl) {
                    Text("Meditate")
                        .font(SerenityTheme.Typography.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                    
                    Spacer()
                    
                    VStack(spacing: SerenityTheme.Spacing.lg) {
                        Image(systemName: "brain.head.profile")
                            .font(.system(size: 60))
                            .foregroundColor(SerenityTheme.Colors.primary)
                        
                        Text("Coming Soon")
                            .font(SerenityTheme.Typography.title2)
                            .foregroundColor(SerenityTheme.Colors.textPrimary)
                        
                        Text("Guided meditation sessions will be available in a future update")
                            .font(SerenityTheme.Typography.body)
                            .foregroundColor(SerenityTheme.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, SerenityTheme.Spacing.xl)
                    }
                    
                Spacer()
            }
            .padding(.horizontal, SerenityTheme.Spacing.lg)
            .serenityOceanBackground()
            .navigationBarHidden(true)
        }
    }
}

#Preview {
    MeditateView()
}
