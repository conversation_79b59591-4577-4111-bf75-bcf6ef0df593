//
//  MoreView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct MoreView: View {
    @StateObject private var authService = AuthenticationService()
    @State private var showingSettings = false
    @State private var showingProfile = false
    @State private var showingAuth = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                    VStack(spacing: SerenityTheme.Spacing.xl) {
                        // Header
                        headerSection
                        
                        // Account Section
                        accountSection
                        
                        // App Settings
                        appSettingsSection
                        
                        // Support Section
                        supportSection
                        
                        // Sign Out
                        signOutSection
                    }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingSettings) {
            SettingsView()
        }
        .sheet(isPresented: $showingProfile) {
            ProfileView()
        }
        .sheet(isPresented: $showingAuth) {
            AuthenticationView()
        }
    }
    
    private var headerSection: some View {
        HStack {
            Text("Settings")
                .font(SerenityTheme.Typography.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            Spacer()
        }
        .padding(.top, SerenityTheme.Spacing.lg)
    }
    
    private var accountSection: some View {
        VStack(spacing: SerenityTheme.Spacing.sm) {
            SettingsRow(
                icon: "person.circle",
                title: "Account",
                subtitle: "Login & Profile",
                action: { showingAuth = true }
            )
            
            SettingsRow(
                icon: "crown",
                title: "Premium",
                subtitle: authService.currentUser?.subscriptionType.displayName ?? "Free",
                action: {}
            )
        }
    }
    
    private var appSettingsSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("App Settings")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            VStack(spacing: SerenityTheme.Spacing.sm) {
                SettingsRow(
                    icon: "bell",
                    title: "Notifications",
                    subtitle: "Reminders and updates",
                    action: {}
                )

                SettingsRow(
                    icon: "paintbrush",
                    title: "Theme",
                    subtitle: "App appearance",
                    action: { showingSettings = true }
                )
            }
        }
    }
    
    private var supportSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Support")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            VStack(spacing: SerenityTheme.Spacing.sm) {
                SettingsRow(
                    icon: "questionmark.circle",
                    title: "Help Center",
                    subtitle: "Get help and support",
                    action: {}
                )
                
                SettingsRow(
                    icon: "envelope",
                    title: "Contact Us",
                    subtitle: "Send us feedback",
                    action: {}
                )
                
                SettingsRow(
                    icon: "doc.text",
                    title: "Terms of Service",
                    subtitle: "Legal information",
                    action: {}
                )
                
                SettingsRow(
                    icon: "hand.raised",
                    title: "Privacy Policy",
                    subtitle: "How we protect your data",
                    action: {}
                )
            }
        }
    }
    
    private var signOutSection: some View {
        VStack(spacing: SerenityTheme.Spacing.md) {
            Button(action: {
                authService.signOut()
            }) {
                HStack {
                    Image(systemName: "rectangle.portrait.and.arrow.right")
                        .font(.system(size: 16))
                    
                    Text("Sign Out")
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.medium)
                }
                .foregroundColor(SerenityTheme.Colors.error)
                .frame(maxWidth: .infinity)
                .padding(.vertical, SerenityTheme.Spacing.md)
                .background(SerenityTheme.Colors.cardBackground)
                .cornerRadius(SerenityTheme.CornerRadius.small)
            }
            
            Text("Version 1.0.0")
                .font(SerenityTheme.Typography.caption2)
                .foregroundColor(SerenityTheme.Colors.textTertiary)
        }
    }
}

struct SettingsRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: SerenityTheme.Spacing.md) {
                Image(systemName: icon)
                    .font(.system(size: 16))
                    .foregroundColor(SerenityTheme.Colors.primary)
                    .frame(width: 24, height: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(SerenityTheme.Typography.callout)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                    
                    Text(subtitle)
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(SerenityTheme.Colors.textTertiary)
            }
            .padding(SerenityTheme.Spacing.md)
            .serenityCardStyle()
        }
    }
}

#Preview {
    MoreView()
}
