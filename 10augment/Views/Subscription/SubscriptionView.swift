//
//  SubscriptionView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct SubscriptionView: View {
    @StateObject private var subscriptionService = SubscriptionService()
    @Environment(\.dismiss) private var dismiss
    @State private var selectedPlan: SubscriptionPlan = SubscriptionPlan.yearly
    
    var body: some View {
        NavigationView {
            ScrollView {
                    VStack(spacing: SerenityTheme.Spacing.xl) {
                        // Header
                        headerSection
                        
                        // Features
                        featuresSection
                        
                        // Plans
                        plansSection
                        
                        // Subscribe Button
                        subscribeButton
                        
                        // Restore Purchases
                        restoreButton
                        
                        // Terms
                        termsSection
                    }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
                .padding(.vertical, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
                }
            }
        }
        .alert("Error", isPresented: .constant(subscriptionService.errorMessage != nil)) {
            Button("OK") {
                subscriptionService.errorMessage = nil
            }
        } message: {
            Text(subscriptionService.errorMessage ?? "")
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: SerenityTheme.Spacing.lg) {
            // Premium Icon
            Circle()
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            SerenityTheme.Colors.warning,
                            SerenityTheme.Colors.warning.opacity(0.7)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 80, height: 80)
                .overlay(
                    Image(systemName: "crown.fill")
                        .font(.system(size: 32))
                        .foregroundColor(.white)
                )
            
            VStack(spacing: SerenityTheme.Spacing.sm) {
                Text("Unlock Premium")
                    .font(SerenityTheme.Typography.title1)
                    .fontWeight(.bold)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)
                
                Text("Access all breathing techniques and advanced features")
                    .font(SerenityTheme.Typography.body)
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    private var featuresSection: some View {
        VStack(spacing: SerenityTheme.Spacing.md) {
            ForEach(premiumFeatures, id: \.title) { feature in
                FeatureRow(
                    icon: feature.icon,
                    title: feature.title,
                    description: feature.description
                )
            }
        }
    }
    
    private var plansSection: some View {
        VStack(spacing: SerenityTheme.Spacing.md) {
            Text("Choose Your Plan")
                .font(SerenityTheme.Typography.title3)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            VStack(spacing: SerenityTheme.Spacing.sm) {
                ForEach(SubscriptionPlan.allPlans, id: \.id) { plan in
                    PlanCard(
                        plan: plan,
                        isSelected: selectedPlan.id == plan.id,
                        action: { selectedPlan = plan }
                    )
                }
            }
        }
    }
    
    private var subscribeButton: some View {
        Button(action: {
            if let product = subscriptionService.availableProducts.first(where: { $0.productIdentifier == selectedPlan.id }) {
                subscriptionService.purchaseProduct(product)
            }
        }) {
            HStack {
                if subscriptionService.isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    Text("Start Free Trial")
                        .font(SerenityTheme.Typography.headline)
                        .fontWeight(.semibold)
                }
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, SerenityTheme.Spacing.md + 4)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        SerenityTheme.Colors.primary,
                        SerenityTheme.Colors.primaryDark
                    ]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(SerenityTheme.CornerRadius.medium)
        }
        .disabled(subscriptionService.isLoading)
    }
    
    private var restoreButton: some View {
        Button(action: {
            subscriptionService.restorePurchases()
        }) {
            Text("Restore Purchases")
                .font(SerenityTheme.Typography.callout)
                .foregroundColor(SerenityTheme.Colors.primary)
        }
    }
    
    private var termsSection: some View {
        VStack(spacing: SerenityTheme.Spacing.sm) {
            Text("7-day free trial, then \(selectedPlan.price) \(selectedPlan.duration)")
                .font(SerenityTheme.Typography.caption1)
                .foregroundColor(SerenityTheme.Colors.textSecondary)
                .multilineTextAlignment(.center)
            
            Text("Cancel anytime. Terms of Service and Privacy Policy apply.")
                .font(SerenityTheme.Typography.caption2)
                .foregroundColor(SerenityTheme.Colors.textTertiary)
                .multilineTextAlignment(.center)
        }
    }
    
    private let premiumFeatures = [
        PremiumFeature(
            icon: "infinity",
            title: "All Breathing Techniques",
            description: "Access to advanced breathing patterns and exercises"
        ),
        PremiumFeature(
            icon: "chart.line.uptrend.xyaxis",
            title: "Advanced Analytics",
            description: "Detailed progress tracking and personalized insights"
        ),
        PremiumFeature(
            icon: "bell.badge",
            title: "Smart Reminders",
            description: "Personalized notifications based on your schedule"
        ),
        PremiumFeature(
            icon: "speaker.slash",
            title: "Ad-Free Experience",
            description: "Enjoy uninterrupted breathing sessions"
        )
    ]
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: SerenityTheme.Spacing.md) {
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(SerenityTheme.Colors.primary)
                .frame(width: 32, height: 32)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(SerenityTheme.Typography.callout)
                    .fontWeight(.medium)
                    .foregroundColor(SerenityTheme.Colors.textPrimary)
                
                Text(description)
                    .font(SerenityTheme.Typography.caption1)
                    .foregroundColor(SerenityTheme.Colors.textSecondary)
            }
            
            Spacer()
        }
    }
}

struct PlanCard: View {
    let plan: SubscriptionPlan
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(plan.name)
                            .font(SerenityTheme.Typography.callout)
                            .fontWeight(.medium)
                            .foregroundColor(SerenityTheme.Colors.textPrimary)
                        
                        if plan.id == SubscriptionPlan.yearly.id {
                            Text("SAVE 50%")
                                .font(SerenityTheme.Typography.caption2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(SerenityTheme.Colors.success)
                                .cornerRadius(4)
                        }
                        
                        if plan.id == SubscriptionPlan.lifetime.id {
                            Text("BEST VALUE")
                                .font(SerenityTheme.Typography.caption2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(SerenityTheme.Colors.warning)
                                .cornerRadius(4)
                        }
                    }
                    
                    Text(plan.description)
                        .font(SerenityTheme.Typography.caption1)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text(plan.price)
                        .font(SerenityTheme.Typography.callout)
                        .fontWeight(.bold)
                        .foregroundColor(SerenityTheme.Colors.textPrimary)
                    
                    Text(plan.duration)
                        .font(SerenityTheme.Typography.caption2)
                        .foregroundColor(SerenityTheme.Colors.textSecondary)
                }
                
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .font(.system(size: 20))
                    .foregroundColor(isSelected ? SerenityTheme.Colors.primary : SerenityTheme.Colors.textTertiary)
            }
            .padding(SerenityTheme.Spacing.md)
            .background(
                isSelected ? SerenityTheme.Colors.primary.opacity(0.1) : SerenityTheme.Colors.cardBackground
            )
            .cornerRadius(SerenityTheme.CornerRadius.medium)
            .overlay(
                RoundedRectangle(cornerRadius: SerenityTheme.CornerRadius.medium)
                    .stroke(
                        isSelected ? SerenityTheme.Colors.primary : SerenityTheme.Colors.textTertiary.opacity(0.2),
                        lineWidth: isSelected ? 2 : 1
                    )
            )
        }
    }
}

struct PremiumFeature {
    let icon: String
    let title: String
    let description: String
}

#Preview {
    SubscriptionView()
}
