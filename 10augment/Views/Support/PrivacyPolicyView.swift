//
//  PrivacyPolicyView.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import SwiftUI

struct PrivacyPolicyView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: SerenityTheme.Spacing.lg) {
                    // Header
                    headerSection
                    
                    // Privacy Content
                    privacyContentSection
                }
                .padding(.horizontal, SerenityTheme.Spacing.lg)
                .padding(.vertical, SerenityTheme.Spacing.lg)
            }
            .serenityOceanBackground()
            .navigationTitle("privacy_policy".localized())
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("done".localized()) {
                        dismiss()
                    }
                    .foregroundColor(SerenityTheme.Colors.primary)
                }
            }
        }
    }
    
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.md) {
            Text("Privacy Policy")
                .font(SerenityTheme.Typography.title1)
                .fontWeight(.bold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            Text("Last updated: July 18, 2025")
                .font(SerenityTheme.Typography.caption1)
                .foregroundColor(SerenityTheme.Colors.textSecondary)
            
            Text("Your privacy is important to us. This Privacy Policy explains how we collect, use, and protect your information when you use Serenity.")
                .font(SerenityTheme.Typography.body)
                .foregroundColor(SerenityTheme.Colors.textSecondary)
        }
    }
    
    private var privacyContentSection: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.xl) {
            PrivacySection(
                title: "1. Information We Collect",
                content: "We collect information you provide directly to us, such as when you create an account, use our breathing exercises, or contact us for support. This may include your name, email address, usage patterns, and preferences."
            )
            
            PrivacySection(
                title: "2. How We Use Your Information",
                content: "We use your information to provide and improve our services, personalize your experience, track your progress, send you updates and notifications, and provide customer support."
            )
            
            PrivacySection(
                title: "3. Information Sharing",
                content: "We do not sell, trade, or rent your personal information to third parties. We may share aggregated, non-personally identifiable information for analytics and research purposes."
            )
            
            PrivacySection(
                title: "4. Data Security",
                content: "We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet is 100% secure."
            )
            
            PrivacySection(
                title: "5. Data Retention",
                content: "We retain your personal information for as long as necessary to provide our services and fulfill the purposes outlined in this policy, unless a longer retention period is required by law."
            )
            
            PrivacySection(
                title: "6. Your Rights",
                content: "You have the right to access, update, or delete your personal information. You can also opt out of certain communications and data collection practices through your account settings."
            )
            
            PrivacySection(
                title: "7. Third-Party Services",
                content: "Our app may integrate with third-party services (such as authentication providers). These services have their own privacy policies, and we encourage you to review them."
            )
            
            PrivacySection(
                title: "8. Children's Privacy",
                content: "Our service is not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13."
            )
            
            PrivacySection(
                title: "9. International Users",
                content: "If you are accessing our service from outside the United States, please be aware that your information may be transferred to and processed in the United States."
            )
            
            PrivacySection(
                title: "10. Changes to Privacy Policy",
                content: "We may update this Privacy Policy from time to time. We will notify you of any material changes by posting the new policy in the app and updating the 'Last updated' date."
            )
            
            PrivacySection(
                title: "11. Contact Us",
                content: "If you have any questions about this Privacy Policy or our data practices, please contact <NAME_EMAIL> or through the Contact Us section in the app."
            )
        }
    }
}

struct PrivacySection: View {
    let title: String
    let content: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: SerenityTheme.Spacing.sm) {
            Text(title)
                .font(SerenityTheme.Typography.headline)
                .fontWeight(.semibold)
                .foregroundColor(SerenityTheme.Colors.textPrimary)
            
            Text(content)
                .font(SerenityTheme.Typography.body)
                .foregroundColor(SerenityTheme.Colors.textSecondary)
                .lineSpacing(4)
        }
        .padding(SerenityTheme.Spacing.lg)
        .serenityCardStyle()
    }
}

#Preview {
    PrivacyPolicyView()
}
