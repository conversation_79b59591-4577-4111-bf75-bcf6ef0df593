//
//  AuthenticationService.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import AuthenticationServices
import Combine

class AuthenticationService: NSObject, ObservableObject {
    static let shared = AuthenticationService()

    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var isLoading = false
    @Published var errorMessage: String?

    private var cancellables = Set<AnyCancellable>()

    override init() {
        super.init()
        checkAuthenticationStatus()
    }
    
    func checkAuthenticationStatus() {
        // Check if user is already logged in
        if let userData = UserDefaults.standard.data(forKey: "currentUser"),
           let user = try? JSONDecoder().decode(User.self, from: userData) {
            self.currentUser = user
            self.isAuthenticated = true
        }
    }
    
    // MARK: - Email Authentication
    func signInWithEmail(email: String, password: String) {
        isLoading = true
        errorMessage = nil
        
        // Simulate API call
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            // For demo purposes, accept any email/password combination
            if !email.isEmpty && !password.isEmpty && email.contains("@") {
                let user = User(email: email, name: email.components(separatedBy: "@").first ?? "User")
                self.setCurrentUser(user)
            } else {
                self.errorMessage = "Invalid email or password"
            }
            self.isLoading = false
        }
    }
    
    func signUpWithEmail(email: String, password: String, name: String) {
        isLoading = true
        errorMessage = nil
        
        // Simulate API call
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            if !email.isEmpty && !password.isEmpty && !name.isEmpty && email.contains("@") {
                let user = User(email: email, name: name)
                self.setCurrentUser(user)
            } else {
                self.errorMessage = "Please fill in all fields with valid information"
            }
            self.isLoading = false
        }
    }
    
    // MARK: - Apple ID Authentication
    func signInWithApple() {
        let request = ASAuthorizationAppleIDProvider().createRequest()
        request.requestedScopes = [.fullName, .email]
        
        let authorizationController = ASAuthorizationController(authorizationRequests: [request])
        authorizationController.performRequests()
    }
    
    // MARK: - Social Authentication Placeholders
    func signInWithGoogle() {
        // Placeholder for Google Sign-In implementation
        isLoading = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let user = User(email: "<EMAIL>", name: "Google User")
            self.setCurrentUser(user)
            self.isLoading = false
        }
    }
    
    func signInWithFacebook() {
        // Placeholder for Facebook Sign-In implementation
        isLoading = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let user = User(email: "<EMAIL>", name: "Facebook User")
            self.setCurrentUser(user)
            self.isLoading = false
        }
    }
    
    func signInWithTikTok() {
        // Placeholder for TikTok Sign-In implementation
        isLoading = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let user = User(email: "<EMAIL>", name: "TikTok User")
            self.setCurrentUser(user)
            self.isLoading = false
        }
    }
    
    // MARK: - Sign Out
    func signOut() {
        currentUser = nil
        isAuthenticated = false
        UserDefaults.standard.removeObject(forKey: "currentUser")
    }
    
    // MARK: - Helper Methods
    private func setCurrentUser(_ user: User) {
        self.currentUser = user
        self.isAuthenticated = true
        
        // Save user to UserDefaults
        if let userData = try? JSONEncoder().encode(user) {
            UserDefaults.standard.set(userData, forKey: "currentUser")
        }
    }
}

// MARK: - ASAuthorizationControllerDelegate
extension AuthenticationService: ASAuthorizationControllerDelegate {
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            let email = appleIDCredential.email ?? "<EMAIL>"
            let name = appleIDCredential.fullName?.formatted() ?? "Apple User"
            
            let user = User(email: email, name: name)
            setCurrentUser(user)
        }
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        errorMessage = "Apple Sign-In failed: \(error.localizedDescription)"
    }
}
