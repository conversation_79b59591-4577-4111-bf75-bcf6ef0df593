//
//  LocalizationManager.swift
//  Serenity
//
//  Created by Serenity Team on 2025/7/18.
//

import Foundation
import SwiftUI

class LocalizationManager: ObservableObject {
    static let shared = LocalizationManager()
    
    @Published var currentLanguage: Language = .english {
        didSet {
            UserDefaults.standard.set(currentLanguage.code, forKey: "selected_language")
            updateBundle()
        }
    }
    
    private var bundle: Bundle = Bundle.main
    
    init() {
        loadSavedLanguage()
        updateBundle()
    }
    
    private func loadSavedLanguage() {
        let savedLanguageCode = UserDefaults.standard.string(forKey: "selected_language") ?? "en"
        currentLanguage = Language.allCases.first { $0.code == savedLanguageCode } ?? .english
    }
    
    private func updateBundle() {
        guard let path = Bundle.main.path(forResource: currentLanguage.code, ofType: "lproj"),
              let bundle = Bundle(path: path) else {
            self.bundle = Bundle.main
            return
        }
        self.bundle = bundle
    }
    
    func localizedString(_ key: String, comment: String = "") -> String {
        return bundle.localizedString(forKey: key, value: nil, table: nil)
    }
    
    func setLanguage(_ language: Language) {
        currentLanguage = language
    }
}

enum Language: String, CaseIterable, Identifiable {
    case english = "en"
    case spanish = "es"
    case french = "fr"
    case german = "de"
    case japanese = "ja"
    case chinese = "zh"
    
    var id: String { rawValue }
    
    var code: String { rawValue }
    
    var displayName: String {
        switch self {
        case .english:
            return "English"
        case .spanish:
            return "Español"
        case .french:
            return "Français"
        case .german:
            return "Deutsch"
        case .japanese:
            return "日本語"
        case .chinese:
            return "中文"
        }
    }
    
    var nativeName: String {
        return displayName
    }
    
    var flag: String {
        switch self {
        case .english:
            return "🇺🇸"
        case .spanish:
            return "🇪🇸"
        case .french:
            return "🇫🇷"
        case .german:
            return "🇩🇪"
        case .japanese:
            return "🇯🇵"
        case .chinese:
            return "🇨🇳"
        }
    }
}

// MARK: - String Extension for Localization
extension String {
    func localized(comment: String = "") -> String {
        return LocalizationManager.shared.localizedString(self, comment: comment)
    }
}

// MARK: - View Extension for Localization
extension View {
    func environmentLocalization() -> some View {
        self.environmentObject(LocalizationManager.shared)
    }
}
